# Memory Master v2

**Memory Master v2** is a comprehensive personal memory layer for Large Language Models (LLMs) that provides private, portable, and open-source memory management. It enables AI assistants to remember context across conversations while giving you complete control over your data.

## 🚀 Features

- **🧠 Intelligent Memory Management**: Advanced memory storage and retrieval using vector embeddings
- **🔌 MCP Server Integration**: Compatible with Claude Desktop, VS Code, and other MCP-enabled applications
- **🎯 Evolution Intelligence**: Automatic memory optimization with ADD/UPDATE/DELETE/NOOP operations
- **🌐 Web Dashboard**: Intuitive UI for managing memories, apps, and system configuration
- **🔒 Privacy-First**: All data stored locally with optional Supabase integration
- **📊 Analytics & Monitoring**: Comprehensive memory access logs and performance metrics
- **🐳 Docker-Ready**: Containerized deployment with Docker Compose
- **⚡ High Performance**: Optimized for <200ms response times with Qdrant vector store

## 🏗️ Architecture Overview

Memory Master v2 follows a microservices architecture with the following components:

### Core Services
- **API Server** (FastAPI): RESTful API and MCP server functionality
- **Vector Store** (Qdrant): High-performance vector database for embeddings
- **Database** (Supabase PostgreSQL): Relational data storage for metadata and relationships
- **Web UI** (Next.js): React-based dashboard for memory management
- **Backup System** (MinIO): Automated backup and recovery capabilities

### Technology Stack
- **Backend**: Python 3.12, FastAPI, SQLAlchemy, mem0ai
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Database**: PostgreSQL (Supabase), Qdrant Vector DB
- **Infrastructure**: Docker, Docker Compose
- **AI/ML**: OpenAI GPT-4, text-embedding-3-small

## 📋 Prerequisites

Before installing Memory Master v2, ensure you have:

- **Docker & Docker Compose** (v20.10+ recommended)
- **OpenAI API Key** (required for LLM operations)
- **4GB+ RAM** (recommended for optimal performance)
- **10GB+ Storage** (for vector embeddings and database)

### Optional Requirements
- **Supabase Account** (for external database hosting)
- **MinIO Server** (for backup storage)
- **Node.js 18+** (for local UI development)
- **Python 3.12+** (for local API development)

## ⚡ Quick Start

### 1. Clone and Setup Environment

```bash
# Clone the repository
git clone <repository-url>
cd memory-master-v2

# Create environment files
cp api/.env.example api/.env
cp ui/.env.example ui/.env
```

### 2. Configure Environment Variables

Edit `api/.env`:
```env
# Required
OPENAI_API_KEY=sk-your-openai-api-key-here
USER=your-unique-user-id

# Database (using external Supabase)
DATABASE_URL=**************************************************/postgres
MIGRATION_MODE=supabase_only

# Optional
API_KEY=your-optional-api-key
```

Edit `ui/.env`:
```env
# Required
NEXT_PUBLIC_API_URL=http://localhost:8765
NEXT_PUBLIC_USER_ID=your-unique-user-id

# Optional Supabase Auth
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_AUTH_ENABLED=false
```

### 3. Launch the System

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### 4. Access the Applications

- **Web Dashboard**: http://localhost:3000
- **API Documentation**: http://localhost:8765/docs
- **MCP Server**: http://localhost:8765 (for Claude Desktop integration)
- **Qdrant Console**: http://localhost:6333/dashboard

## 🔧 Configuration

### Memory Configuration

Memory Master v2 supports extensive configuration through the web UI or API:

```json
{
  "mem0": {
    "version": "v1.1",
    "llm": {
      "provider": "openai",
      "config": {
        "model": "gpt-4o-mini",
        "temperature": 0.1,
        "max_tokens": 2000
      }
    },
    "embedder": {
      "provider": "openai", 
      "config": {
        "model": "text-embedding-3-small"
      }
    }
  }
}
```

### Evolution Intelligence

Enable advanced memory evolution with custom prompts:

```json
{
  "custom_fact_extraction_prompt": "Extract technical facts...",
  "custom_update_memory_prompt": "Determine memory operations..."
}
```

## 🔌 MCP Integration

### Claude Desktop Setup

1. Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "memory-master": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765"
      }
    }
  }
}
```

2. Restart Claude Desktop and verify the connection.

### Available MCP Tools

- `add_memories`: Store new memories with automatic chunking
- `search_memory`: Semantic search across stored memories  
- `list_memories`: Browse memories with filtering options
- `get_system_health`: Monitor system performance
- `get_evolution_metrics`: View memory optimization statistics

## 📁 Project Structure

```
memory-master-v2/
├── api/                    # Backend API & MCP Server
│   ├── app/               # FastAPI application
│   │   ├── routers/       # API route handlers
│   │   ├── services/      # Business logic services
│   │   ├── utils/         # Utility functions
│   │   ├── auth/          # Authentication middleware
│   │   └── evolution/     # Evolution intelligence system
│   ├── alembic/           # Database migrations
│   ├── tests/             # Test suite
│   ├── Dockerfile         # API container configuration
│   └── requirements.txt   # Python dependencies
├── ui/                    # Frontend Web Dashboard
│   ├── app/               # Next.js app directory
│   ├── components/        # React components
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   ├── store/             # State management
│   ├── styles/            # CSS and styling
│   ├── Dockerfile         # UI container configuration
│   └── package.json       # Node.js dependencies
├── backup-scripts/        # Automated backup system
├── mcp-servers/           # MCP server configurations
├── docker-compose.yml     # Multi-container orchestration
└── README.md              # This file
```

## 🚨 Troubleshooting

### Common Issues

**1. Memory Client Initialization Fails**
```bash
# Check OpenAI API key
echo $OPENAI_API_KEY

# Verify Qdrant connectivity
curl http://localhost:6333/health
```

**2. Database Connection Issues**
```bash
# Check Supabase connectivity
docker-compose logs openmemory-mcp | grep -i database
```

**3. UI Not Loading**
```bash
# Manual UI startup
cd ui && pnpm install && pnpm dev
```

**4. MCP Server Not Responding**
```bash
# Check API server logs
docker-compose logs openmemory-mcp
```

### Performance Optimization

- **Memory Usage**: Limit Qdrant memory to 2GB for optimal performance
- **Response Time**: Enable caching for <200ms response times
- **Concurrent Users**: Scale with multiple API workers (default: 4)

### Known Limitations

- **Text Length**: Large texts (>2000 words) are automatically chunked
- **Claude Desktop**: Use concise memories for best compatibility
- **Vector Store**: Requires restart after configuration changes

## 📚 Additional Resources

- **API Documentation**: http://localhost:8765/docs
- **Evolution Intelligence Guide**: See DeveloperManual.md
- **MCP Integration Examples**: See UserManual.md
- **Backup & Recovery**: See backup-scripts/ directory

## 🤝 Contributing

Memory Master v2 is designed for extensibility. See DeveloperManual.md for:
- Development setup and guidelines
- Architecture deep-dive
- Adding new features
- Testing strategies

## 📄 License

This project is open-source. See LICENSE file for details.

---

**Memory Master v2** - Your intelligent memory layer for AI assistants.

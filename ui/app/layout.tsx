import type React from "react";
import "@/app/globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Navbar } from "@/components/Navbar";
import { Toaster } from "@/components/ui/toaster";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Providers } from "./providers";

export const metadata = {
  title: "MemoryMaster - Developer Dashboard",
  description: "Manage your MemoryMaster integration and stored memories",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/logo.svg" type="image/svg+xml" />
        <link rel="shortcut icon" href="/logo.svg" />
      </head>
      <body className="h-screen font-sans antialiased flex flex-col bg-zinc-950 overflow-x-hidden">
        <Providers>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem
            disableTransitionOnChange
          >
            <Navbar />
            <ScrollArea className="h-[calc(100vh-64px)] w-full overflow-x-hidden">
              {children}
              <footer className="border-t border-zinc-800 bg-zinc-950/50 py-4 mt-8">
                <div className="container mx-auto text-center">
                  <p className="text-sm text-zinc-400">
                    Made with <span className="text-red-500">♥️</span> by Aung Hein Aye
                  </p>
                </div>
              </footer>
            </ScrollArea>
            <Toaster />
          </ThemeProvider>
        </Providers>
      </body>
    </html>
  );
}

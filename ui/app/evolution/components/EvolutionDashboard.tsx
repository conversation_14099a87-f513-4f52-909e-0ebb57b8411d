"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp } from "lucide-react";
import { KeyMetricsDisplay } from "./KeyMetricsDisplay";
import { EvolutionTimeline } from "./EvolutionTimeline";
import { OperationBreakdown } from "./OperationBreakdown";
import { RealTimeActivityFeed } from "./RealTimeActivityFeed";

export function EvolutionDashboard() {
  const [collapsedSections, setCollapsedSections] = useState({
    metrics: false,
    breakdown: false,
    timeline: false,
    activity: false,
  });

  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Top Row - Metrics and Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Key Metrics Display */}
        <div className="md:col-span-1 lg:col-span-2">
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold">Key Metrics</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  onClick={() => toggleSection('metrics')}
                >
                  {collapsedSections.metrics ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
                </Button>
              </div>
            </CardHeader>
            <CardContent className={`${collapsedSections.metrics ? 'hidden md:block' : 'block'}`}>
              <KeyMetricsDisplay />
            </CardContent>
          </Card>
        </div>
        
        {/* Operation Breakdown */}
        <div className="md:col-span-1 lg:col-span-1">
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold">Operations</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  onClick={() => toggleSection('breakdown')}
                >
                  {collapsedSections.breakdown ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
                </Button>
              </div>
            </CardHeader>
            <CardContent className={`${collapsedSections.breakdown ? 'hidden md:block' : 'block'}`}>
              <OperationBreakdown />
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Evolution Timeline - Full width */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Evolution Timeline</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              className="sm:hidden"
              onClick={() => toggleSection('timeline')}
            >
              {collapsedSections.timeline ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>
        <CardContent className={`${collapsedSections.timeline ? 'hidden sm:block' : 'block'}`}>
          <EvolutionTimeline />
        </CardContent>
      </Card>
      
      {/* Real-Time Activity Feed - Full width */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Real-Time Activity</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              className="sm:hidden"
              onClick={() => toggleSection('activity')}
            >
              {collapsedSections.activity ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>
        <CardContent className={`${collapsedSections.activity ? 'hidden sm:block' : 'block'}`}>
          <RealTimeActivityFeed />
        </CardContent>
      </Card>
    </div>
  );
}
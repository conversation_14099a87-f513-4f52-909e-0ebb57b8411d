"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Activity,
  Search,
  Filter,
  Download,
  RefreshCw,
  Play,
  Pause,
  ChevronDown,
  ChevronUp,
  Clock,
  User,
  Brain,
  Target
} from "lucide-react";
import { useEvolutionAnalytics, EvolutionAnalyticsItem } from "@/hooks/useEvolutionAnalytics";
import { cn } from "@/lib/utils";

interface ActivityFilters {
  operationType: 'all' | 'ADD' | 'UPDATE' | 'DELETE' | 'NOOP';
  confidenceLevel: 'all' | 'high' | 'medium' | 'low';
  timeRange: '1h' | '6h' | '24h' | '7d';
  searchTerm: string;
}

const operationConfig = {
  ADD: {
    color: 'bg-green-500',
    textColor: 'text-green-400',
    borderColor: 'border-green-500/20',
    icon: <Target className="h-3 w-3" />,
    label: 'ADD'
  },
  UPDATE: {
    color: 'bg-blue-500',
    textColor: 'text-blue-400',
    borderColor: 'border-blue-500/20',
    icon: <Brain className="h-3 w-3" />,
    label: 'UPD'
  },
  DELETE: {
    color: 'bg-red-500',
    textColor: 'text-red-400',
    borderColor: 'border-red-500/20',
    icon: <Target className="h-3 w-3" />,
    label: 'DEL'
  },
  NOOP: {
    color: 'bg-yellow-500',
    textColor: 'text-yellow-400',
    borderColor: 'border-yellow-500/20',
    icon: <Clock className="h-3 w-3" />,
    label: 'NOOP'
  }
};

export function RealTimeActivityFeed() {
  const [activities, setActivities] = useState<EvolutionAnalyticsItem[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<EvolutionAnalyticsItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [sortBy, setSortBy] = useState<'timestamp' | 'operation_type' | 'confidence'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);

  const [filters, setFilters] = useState<ActivityFilters>({
    operationType: 'all',
    confidenceLevel: 'all',
    timeRange: '24h',
    searchTerm: ''
  });

  const { isLoading, fetchAnalytics, exportToCsv } = useEvolutionAnalytics();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const ITEMS_PER_PAGE = 25;
  const REFRESH_INTERVAL = 10000; // 10 seconds

  // Load initial data
  useEffect(() => {
    loadActivities(true);
  }, []);

  // Set up real-time polling
  useEffect(() => {
    if (isRealTimeEnabled) {
      intervalRef.current = setInterval(() => {
        loadActivities(false);
      }, REFRESH_INTERVAL);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRealTimeEnabled]);

  // Apply filters and sorting
  useEffect(() => {
    applyFiltersAndSort();
  }, [activities, filters, sortBy, sortOrder]);

  const loadActivities = async (isInitial: boolean = false) => {
    try {
      const response = await fetchAnalytics({
        size: isInitial ? ITEMS_PER_PAGE : 1000,
        ...(filters.operationType !== 'all' && { operation_type: filters.operationType })
      });

      if (isInitial) {
        setActivities(response.items);
      } else {
        // Merge new data, avoiding duplicates
        setActivities(prev => {
          const existingIds = new Set(prev.map(item => item.id));
          const newItems = response.items.filter(item => !existingIds.has(item.id));
          return [...newItems, ...prev].slice(0, 1000); // Keep max 1000 items
        });
      }

      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to load activities:', error);
    }
  };

  const applyFiltersAndSort = () => {
    let filtered = [...activities];

    // Apply operation type filter
    if (filters.operationType !== 'all') {
      filtered = filtered.filter(item => item.operation_type === filters.operationType);
    }

    // Apply confidence level filter
    if (filters.confidenceLevel !== 'all') {
      filtered = filtered.filter(item => {
        const confidence = item.confidence_score;
        switch (filters.confidenceLevel) {
          case 'high': return confidence >= 0.8;
          case 'medium': return confidence >= 0.5 && confidence < 0.8;
          case 'low': return confidence < 0.5;
          default: return true;
        }
      });
    }

    // Apply time range filter
    const now = new Date();
    const timeRangeMs = {
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000
    };
    const cutoffTime = new Date(now.getTime() - timeRangeMs[filters.timeRange]);
    filtered = filtered.filter(item => new Date(item.timestamp) >= cutoffTime);

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.reasoning?.toLowerCase().includes(searchLower) ||
        item.id.toLowerCase().includes(searchLower) ||
        item.operation_type.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'timestamp':
          aValue = new Date(a.timestamp).getTime();
          bValue = new Date(b.timestamp).getTime();
          break;
        case 'operation_type':
          aValue = a.operation_type;
          bValue = b.operation_type;
          break;
        case 'confidence':
          aValue = a.confidence_score;
          bValue = b.confidence_score;
          break;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredActivities(filtered);
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const visibleIds = getCurrentPageItems().map(item => item.id);
      setSelectedItems(new Set([...selectedItems, ...visibleIds]));
    } else {
      const visibleIds = new Set(getCurrentPageItems().map(item => item.id));
      setSelectedItems(new Set([...selectedItems].filter(id => !visibleIds.has(id))));
    }
  };

  const handleExportSelected = () => {
    const selectedActivities = activities.filter(item => selectedItems.has(item.id));
    exportToCsv(selectedActivities, `selected-activities-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const handleExportAll = () => {
    exportToCsv(filteredActivities, `activity-feed-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredActivities.slice(startIndex, endIndex);
  };

  const totalPages = Math.ceil(filteredActivities.length / ITEMS_PER_PAGE);
  const currentPageItems = getCurrentPageItems();
  const allCurrentPageSelected = currentPageItems.length > 0 &&
    currentPageItems.every(item => selectedItems.has(item.id));

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.5) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) return { variant: 'default' as const, label: 'High' };
    if (confidence >= 0.5) return { variant: 'secondary' as const, label: 'Med' };
    return { variant: 'destructive' as const, label: 'Low' };
  };

  return (
    <Card className="border-zinc-800 bg-zinc-950/50">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Real-Time Activity Feed
          <div className={cn(
            "w-2 h-2 rounded-full ml-2",
            isRealTimeEnabled ? "bg-green-500 animate-pulse" : "bg-zinc-500"
          )} />
        </CardTitle>
        <p className="text-sm text-zinc-400 mb-4">
          Live memory operations updating every 10 seconds • Last update: {lastUpdate.toLocaleTimeString()}
        </p>

        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsRealTimeEnabled(!isRealTimeEnabled)}
              className={cn(
                "border-zinc-700",
                isRealTimeEnabled ? "bg-green-600/20 border-green-600/50 hover:bg-green-600/30" : "hover:bg-zinc-800"
              )}
            >
              {isRealTimeEnabled ? <Pause className="h-4 w-4 mr-1" /> : <Play className="h-4 w-4 mr-1" />}
              {isRealTimeEnabled ? 'Pause' : 'Resume'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => loadActivities(true)}
              disabled={isLoading}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Filter className="h-4 w-4 mr-1" />
              Filters
              {showFilters ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />}
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportSelected}
              disabled={selectedItems.size === 0}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Download className="h-4 w-4 mr-1" />
              Export Selected ({selectedItems.size})
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportAll}
              disabled={filteredActivities.length === 0}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Download className="h-4 w-4 mr-1" />
              Export All
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-zinc-900/50 rounded-lg border border-zinc-700 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div>
                <label className="text-xs text-zinc-400 mb-2 block">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
                  <Input
                    placeholder="Search operations..."
                    value={filters.searchTerm}
                    onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                    className="pl-10 bg-zinc-800 border-zinc-600 text-sm"
                  />
                </div>
              </div>

              {/* Operation Type */}
              <div>
                <label className="text-xs text-zinc-400 mb-2 block">Operation Type</label>
                <select
                  value={filters.operationType}
                  onChange={(e) => setFilters(prev => ({ ...prev, operationType: e.target.value as any }))}
                  className="w-full bg-zinc-800 border border-zinc-600 rounded px-3 py-2 text-sm text-white"
                >
                  <option value="all">All Operations</option>
                  <option value="ADD">ADD</option>
                  <option value="UPDATE">UPDATE</option>
                  <option value="DELETE">DELETE</option>
                  <option value="NOOP">NOOP</option>
                </select>
              </div>

              {/* Confidence Level */}
              <div>
                <label className="text-xs text-zinc-400 mb-2 block">Confidence</label>
                <select
                  value={filters.confidenceLevel}
                  onChange={(e) => setFilters(prev => ({ ...prev, confidenceLevel: e.target.value as any }))}
                  className="w-full bg-zinc-800 border border-zinc-600 rounded px-3 py-2 text-sm text-white"
                >
                  <option value="all">All Levels</option>
                  <option value="high">High (≥80%)</option>
                  <option value="medium">Medium (50-79%)</option>
                  <option value="low">Low (&lt;50%)</option>
                </select>
              </div>

              {/* Time Range */}
              <div>
                <label className="text-xs text-zinc-400 mb-2 block">Time Range</label>
                <select
                  value={filters.timeRange}
                  onChange={(e) => setFilters(prev => ({ ...prev, timeRange: e.target.value as any }))}
                  className="w-full bg-zinc-800 border border-zinc-600 rounded px-3 py-2 text-sm text-white"
                >
                  <option value="1h">Last Hour</option>
                  <option value="6h">Last 6 Hours</option>
                  <option value="24h">Last 24 Hours</option>
                  <option value="7d">Last 7 Days</option>
                </select>
              </div>
            </div>

            {/* Sort Options */}
            <div className="flex items-center gap-4">
              <div>
                <label className="text-xs text-zinc-400 mb-2 block">Sort By</label>
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field as any);
                    setSortOrder(order as any);
                  }}
                  className="bg-zinc-800 border border-zinc-600 rounded px-3 py-2 text-sm text-white"
                >
                  <option value="timestamp-desc">Newest First</option>
                  <option value="timestamp-asc">Oldest First</option>
                  <option value="confidence-desc">Highest Confidence</option>
                  <option value="confidence-asc">Lowest Confidence</option>
                  <option value="operation_type-asc">Operation A-Z</option>
                  <option value="operation_type-desc">Operation Z-A</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {isLoading && activities.length === 0 ? (
          <div className="h-64 flex items-center justify-center">
            <div className="text-zinc-400">Loading activity feed...</div>
          </div>
        ) : filteredActivities.length === 0 ? (
          <div className="h-64 flex items-center justify-center">
            <div className="text-zinc-400">No activities found</div>
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Checkbox
                  checked={allCurrentPageSelected}
                  onCheckedChange={handleSelectAll}
                  className="border-zinc-600"
                />
                <span className="text-sm text-zinc-400">
                  Select all on page ({currentPageItems.length} items)
                </span>
              </div>
              <div className="text-sm text-zinc-400">
                Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1}-{Math.min(currentPage * ITEMS_PER_PAGE, filteredActivities.length)} of {filteredActivities.length}
              </div>
            </div>

            {/* Activity List */}
            <ScrollArea ref={scrollAreaRef} className="h-96">
              <div className="space-y-2">
                {currentPageItems.map((activity) => {
                  const config = operationConfig[activity.operation_type];
                  const confidenceBadge = getConfidenceBadge(activity.confidence_score);
                  const isSelected = selectedItems.has(activity.id);

                  return (
                    <div
                      key={activity.id}
                      className={cn(
                        'p-3 rounded-lg border transition-colors',
                        isSelected
                          ? 'bg-blue-600/20 border-blue-600/50'
                          : 'bg-zinc-800/30 border-zinc-700 hover:bg-zinc-800/50'
                      )}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => handleSelectItem(activity.id, checked as boolean)}
                          className="border-zinc-600 mt-1"
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge
                                className={cn('text-xs px-2 py-1', config.color)}
                              >
                                {config.icon}
                                <span className="ml-1">{config.label}</span>
                              </Badge>

                              <Badge variant={confidenceBadge.variant} className="text-xs">
                                {confidenceBadge.label}
                              </Badge>

                              <span className={cn('text-sm font-medium', getConfidenceColor(activity.confidence_score))}>
                                {Math.round(activity.confidence_score * 100)}%
                              </span>

                              {activity.processing_time_ms && (
                                <span className="text-xs text-zinc-500">
                                  {activity.processing_time_ms}ms
                                </span>
                              )}
                            </div>

                            <div className="text-xs text-zinc-400">
                              {formatTimestamp(activity.timestamp)}
                            </div>
                          </div>

                          {activity.reasoning && (
                            <p className="text-sm text-zinc-300 mb-2 line-clamp-2">
                              {activity.reasoning}
                            </p>
                          )}

                          <div className="flex items-center justify-between text-xs text-zinc-500">
                            <div className="flex items-center gap-4">
                              <span>ID: {activity.id.slice(0, 8)}...</span>
                              {activity.similarity_score && (
                                <span>Similarity: {Math.round(activity.similarity_score * 100)}%</span>
                              )}
                            </div>
                            {activity.app_id && (
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>App: {activity.app_id.slice(0, 8)}...</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t border-zinc-700">
                <div className="text-sm text-zinc-400">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="border-zinc-700 hover:bg-zinc-800"
                  >
                    Previous
                  </Button>

                  {/* Page numbers */}
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                      return (
                        <Button
                          key={pageNum}
                          variant={pageNum === currentPage ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                          className={cn(
                            "w-8 h-8 p-0 text-xs",
                            pageNum === currentPage
                              ? "bg-blue-600 hover:bg-blue-700"
                              : "border-zinc-700 hover:bg-zinc-800"
                          )}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="border-zinc-700 hover:bg-zinc-800"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
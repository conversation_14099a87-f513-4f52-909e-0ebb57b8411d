"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon, <PERSON><PERSON>hart3, Refresh<PERSON>w } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { useEvolutionAnalytics } from "@/hooks/useEvolutionAnalytics";
import { OperationDetailModal } from "./OperationDetailModal";
import { cn } from "@/lib/utils";

interface OperationData {
  name: 'ADD' | 'UPDATE' | 'DELETE' | 'NOOP';
  value: number;
  percentage: number;
  color: string;
  description: string;
}

const OPERATION_COLORS = {
  ADD: '#10B981',     // Green
  UPDATE: '#3B82F6',  // Blue
  DELETE: '#EF4444',  // Red
  NOOP: '#F59E0B'     // Yellow
};

const OPERATION_DESCRIPTIONS = {
  ADD: 'New memories created',
  UPDATE: 'Existing memories enhanced',
  DELETE: 'Outdated memories removed',
  NOOP: 'No action required'
};

export function OperationBreakdown() {
  const [operationData, setOperationData] = useState<OperationData[]>([]);
  const [selectedOperation, setSelectedOperation] = useState<'ADD' | 'UPDATE' | 'DELETE' | 'NOOP' | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'pie' | 'bar'>('pie');

  const { isLoading, error, fetchAnalytics } = useEvolutionAnalytics();

  useEffect(() => {
    loadOperationData();
  }, []);

  const loadOperationData = async () => {
    try {
      // Fetch data for each operation type
      const [addData, updateData, deleteData, noopData] = await Promise.all([
        fetchAnalytics({ operation_type: 'ADD', size: 1000 }),
        fetchAnalytics({ operation_type: 'UPDATE', size: 1000 }),
        fetchAnalytics({ operation_type: 'DELETE', size: 1000 }),
        fetchAnalytics({ operation_type: 'NOOP', size: 1000 })
      ]);

      const total = addData.total + updateData.total + deleteData.total + noopData.total;

      const data: OperationData[] = [
        {
          name: 'ADD',
          value: addData.total,
          percentage: total > 0 ? Math.round((addData.total / total) * 100) : 0,
          color: OPERATION_COLORS.ADD,
          description: OPERATION_DESCRIPTIONS.ADD
        },
        {
          name: 'UPDATE',
          value: updateData.total,
          percentage: total > 0 ? Math.round((updateData.total / total) * 100) : 0,
          color: OPERATION_COLORS.UPDATE,
          description: OPERATION_DESCRIPTIONS.UPDATE
        },
        {
          name: 'DELETE',
          value: deleteData.total,
          percentage: total > 0 ? Math.round((deleteData.total / total) * 100) : 0,
          color: OPERATION_COLORS.DELETE,
          description: OPERATION_DESCRIPTIONS.DELETE
        },
        {
          name: 'NOOP',
          value: noopData.total,
          percentage: total > 0 ? Math.round((noopData.total / total) * 100) : 0,
          color: OPERATION_COLORS.NOOP,
          description: OPERATION_DESCRIPTIONS.NOOP
        }
      ].filter(item => item.value > 0); // Only show operations that have data

      setOperationData(data);
    } catch (error) {
      console.error('Failed to load operation data:', error);
    }
  };

  const handleSegmentClick = (data: any) => {
    if (data && data.name) {
      setSelectedOperation(data.name);
      setIsModalOpen(true);
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-zinc-900 border border-zinc-700 rounded-lg p-3 shadow-lg">
          <p className="text-white font-medium">{data.name} Operations</p>
          <p className="text-zinc-300 text-sm">{data.description}</p>
          <div className="mt-2 space-y-1">
            <div className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: data.color }}
              />
              <span className="text-white font-medium">{data.value} operations</span>
            </div>
            <p className="text-zinc-400 text-sm">{data.percentage}% of total</p>
          </div>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (percent < 0.05) return null; // Don't show labels for very small segments

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (error) {
    return (
      <Card className="border-zinc-800 bg-zinc-950/50">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <PieChartIcon className="h-5 w-5" />
            Operation Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-red-400">
            Error loading operation data: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalOperations = operationData.reduce((sum, item) => sum + item.value, 0);

  return (
    <>
      <Card className="border-zinc-800 bg-zinc-950/50">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <PieChartIcon className="h-5 w-5" />
            Operation Breakdown
          </CardTitle>
          <p className="text-sm text-zinc-400 mb-4">
            Interactive distribution of memory operations
          </p>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'pie' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('pie')}
                className={cn(
                  "text-xs",
                  viewMode === 'pie'
                    ? "bg-blue-600 hover:bg-blue-700"
                    : "border-zinc-700 hover:bg-zinc-800"
                )}
              >
                <PieChartIcon className="h-4 w-4 mr-1" />
                Pie
              </Button>
              <Button
                variant={viewMode === 'bar' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('bar')}
                className={cn(
                  "text-xs",
                  viewMode === 'bar'
                    ? "bg-blue-600 hover:bg-blue-700"
                    : "border-zinc-700 hover:bg-zinc-800"
                )}
              >
                <BarChart3 className="h-4 w-4 mr-1" />
                Bar
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={loadOperationData}
              disabled={isLoading}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="text-zinc-400">Loading operation data...</div>
            </div>
          ) : operationData.length === 0 ? (
            <div className="h-64 flex items-center justify-center">
              <div className="text-zinc-400">No operation data available</div>
            </div>
          ) : (
            <>
              {/* Chart */}
              <div className="h-64 mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={operationData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={CustomLabel}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      onClick={handleSegmentClick}
                      className="cursor-pointer"
                      animationBegin={0}
                      animationDuration={800}
                    >
                      {operationData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.color}
                          stroke={entry.color}
                          strokeWidth={2}
                          className="hover:opacity-80 transition-opacity"
                        />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Legend with detailed stats */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm text-zinc-400 border-b border-zinc-700 pb-2">
                  <span>Operation Type</span>
                  <span>Count / Percentage</span>
                </div>

                {operationData.map((item) => (
                  <div
                    key={item.name}
                    className="flex items-center justify-between p-3 rounded-lg bg-zinc-800/30 hover:bg-zinc-800/50 cursor-pointer transition-colors"
                    onClick={() => {
                      setSelectedOperation(item.name);
                      setIsModalOpen(true);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: item.color }}
                      />
                      <div>
                        <span className="text-white font-medium">{item.name}</span>
                        <p className="text-xs text-zinc-400">{item.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-medium">{item.value.toLocaleString()}</div>
                      <div className="text-xs text-zinc-400">{item.percentage}%</div>
                    </div>
                  </div>
                ))}

                {/* Total */}
                <div className="flex items-center justify-between pt-3 border-t border-zinc-700 text-sm">
                  <span className="text-zinc-400">Total Operations</span>
                  <span className="text-white font-medium">{totalOperations.toLocaleString()}</span>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Operation Detail Modal */}
      {selectedOperation && (
        <OperationDetailModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedOperation(null);
          }}
          operationType={selectedOperation}
          totalCount={operationData.find(op => op.name === selectedOperation)?.value || 0}
        />
      )}
    </>
  );
}
"use client";

import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Minus, Brain, Target, Star, BarChart3, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { useKeyMetrics } from "@/hooks/useKeyMetrics";

interface MetricCardProps {
  title: string;
  value: string | number;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    percentage: number;
    period: string;
  };
  icon: React.ReactNode;
  description?: string;
  color?: 'green' | 'blue' | 'purple' | 'orange';
}

function MetricCard({ title, value, trend, icon, description, color = 'blue' }: MetricCardProps) {
  const colorClasses = {
    green: 'text-green-400 bg-green-400/10 border-green-400/20',
    blue: 'text-blue-400 bg-blue-400/10 border-blue-400/20',
    purple: 'text-purple-400 bg-purple-400/10 border-purple-400/20',
    orange: 'text-orange-400 bg-orange-400/10 border-orange-400/20',
  };

  const getTrendIcon = () => {
    switch (trend?.direction) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return <Minus className="h-3 w-3" />;
    }
  };

  const getTrendColor = () => {
    switch (trend?.direction) {
      case 'up':
        return 'text-green-400';
      case 'down':
        return 'text-red-400';
      default:
        return 'text-zinc-400';
    }
  };

  return (
    <Card className={cn('border', colorClasses[color])}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-zinc-300">{title}</CardTitle>
        <div className={cn('p-2 rounded-lg', colorClasses[color])}>
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-white mb-1">{value}</div>
        {description && (
          <p className="text-xs text-zinc-400 mb-2">{description}</p>
        )}
        {trend && (
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className={cn('text-xs', getTrendColor(), 'border-current')}>
              {getTrendIcon()}
              {trend.percentage}%
            </Badge>
            <span className="text-xs text-zinc-400">vs {trend.period}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function KeyMetricsDisplay() {
  const { metrics, isLoading, error, fetchMetrics, refetch } = useKeyMetrics('week', true);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 30000);

    return () => clearInterval(interval);
  }, [refetch]);

  const handleRefresh = () => {
    refetch();
  };

  // Default values when loading or no data
  const defaultMetrics = {
    learning_efficiency: {
      value: '0.0%',
      trend: { direction: 'neutral' as const, percentage: 0, period: 'last week' },
      description: 'Memory consolidation rate'
    },
    conflict_resolution: {
      value: '0.0%',
      trend: { direction: 'neutral' as const, percentage: 0, period: 'last week' },
      description: 'Successful conflict handling'
    },
    memory_quality: {
      value: '0.0/10',
      trend: { direction: 'neutral' as const, percentage: 0, period: 'last week' },
      description: 'Average quality score'
    },
    operation_distribution: {
      value: '0',
      trend: { direction: 'neutral' as const, percentage: 0, period: 'last week' },
      description: 'Total operations this week'
    }
  };

  const displayMetrics = metrics || defaultMetrics;

  return (
    <Card className="border-zinc-800 bg-zinc-950/50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-white">Key Metrics</CardTitle>
            <p className="text-sm text-zinc-400">
              Real-time performance indicators for memory evolution operations
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="border-zinc-700 hover:border-zinc-600"
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
        {error && (
          <div className="text-sm text-red-400 bg-red-400/10 border border-red-400/20 rounded-lg p-2 mt-2">
            Error loading metrics: {error}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="border-zinc-800 bg-zinc-900/50 animate-pulse">
                <CardHeader className="pb-2">
                  <div className="h-4 bg-zinc-700 rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-zinc-700 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-zinc-700 rounded w-full mb-2"></div>
                  <div className="h-4 bg-zinc-700 rounded w-1/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Learning Efficiency"
              value={displayMetrics.learning_efficiency.value}
              trend={displayMetrics.learning_efficiency.trend}
              icon={<Brain className="h-4 w-4" />}
              description={displayMetrics.learning_efficiency.description}
              color="green"
            />

            <MetricCard
              title="Conflict Resolution"
              value={displayMetrics.conflict_resolution.value}
              trend={displayMetrics.conflict_resolution.trend}
              icon={<Target className="h-4 w-4" />}
              description={displayMetrics.conflict_resolution.description}
              color="blue"
            />

            <MetricCard
              title="Memory Quality Score"
              value={displayMetrics.memory_quality.value}
              trend={displayMetrics.memory_quality.trend}
              icon={<Star className="h-4 w-4" />}
              description={displayMetrics.memory_quality.description}
              color="purple"
            />

            <MetricCard
              title="Operation Distribution"
              value={displayMetrics.operation_distribution.value}
              trend={displayMetrics.operation_distribution.trend}
              icon={<BarChart3 className="h-4 w-4" />}
              description={displayMetrics.operation_distribution.description}
              color="orange"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
"use client";

import { Suspense } from "react";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { EvolutionDashboard } from "./components/EvolutionDashboard";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import "@/styles/animation.css";

// Loading component for the dashboard
function DashboardLoading() {
  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <div className="md:col-span-1 lg:col-span-2">
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardContent className="p-6">
              <Skeleton className="h-32 w-full bg-zinc-800" />
            </CardContent>
          </Card>
        </div>
        <div className="md:col-span-1 lg:col-span-1">
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardContent className="p-6">
              <Skeleton className="h-32 w-full bg-zinc-800" />
            </CardContent>
          </Card>
        </div>
      </div>
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardContent className="p-6">
          <Skeleton className="h-48 w-full bg-zinc-800" />
        </CardContent>
      </Card>
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardContent className="p-6">
          <Skeleton className="h-64 w-full bg-zinc-800" />
        </CardContent>
      </Card>
    </div>
  );
}

export default function EvolutionPage() {
  return (
    <ProtectedRoute>
      <div className="text-white py-4 sm:py-6">
        <div className="container px-4 sm:px-6 lg:px-8">
          <div className="w-full mx-auto space-y-4 sm:space-y-6">
            <div className="animate-fade-slide-down">
              <h1 className="text-2xl sm:text-3xl font-bold mb-2">Evolution Intelligence Dashboard</h1>
              <p className="text-zinc-400 mb-4 sm:mb-6 text-sm sm:text-base">
                Monitor and analyze memory evolution operations in real-time
              </p>
            </div>
            
            <div className="animate-fade-slide-down delay-1">
              <Suspense fallback={<DashboardLoading />}>
                <EvolutionDashboard />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
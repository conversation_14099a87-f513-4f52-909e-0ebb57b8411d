@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 260 94% 59%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 260 94% 59%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 260 94% 59%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 260 94% 59%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile responsiveness fixes */
@layer utilities {
  /* Prevent horizontal overflow on mobile */
  .mobile-container {
    @apply w-full max-w-full overflow-x-hidden;
  }

  /* Ensure proper container behavior */
  .container {
    @apply w-full max-w-full px-4 mx-auto;
  }

  /* Fix for mobile viewport */
  @media (max-width: 768px) {
    .container {
      @apply px-2 max-w-full;
      width: 100% !important;
    }

    /* Prevent content from extending beyond viewport */
    body, html {
      @apply overflow-x-hidden;
      max-width: 100vw;
    }

    /* Fix for any element that might cause horizontal scroll */
    * {
      max-width: 100%;
      box-sizing: border-box;
    }

    /* Fix for grid layouts on mobile */
    .mobile-grid-fix {
      @apply grid-cols-1 gap-2;
    }

    /* Responsive text sizing */
    .mobile-text {
      @apply text-sm;
    }

    /* Responsive button sizing */
    .mobile-button {
      @apply text-xs px-2 py-1;
    }

    /* Ensure cards don't overflow */
    .card {
      @apply w-full max-w-full;
    }

    /* Fix for tabs that might overflow */
    [role="tablist"] {
      @apply overflow-x-auto;
    }
  }
}

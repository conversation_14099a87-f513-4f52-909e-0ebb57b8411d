"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import {
  Activity,
  Brain,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Settings,
  ExternalLink,
  FileBarChart,
  BarChart3,
  Info,
  HelpCircle
} from "lucide-react";
import { SystemStatusDialog } from "./SystemStatusDialog";
import { PerformanceReportDialog } from "./PerformanceReportDialog";
import { useEvolutionSettings } from "@/hooks/useEvolutionSettings";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface OverviewTabProps {
  onSettingsChange: (hasChanges: boolean) => void;
}

export function OverviewTab({ onSettingsChange }: OverviewTabProps) {
  const router = useRouter();
  const { toast } = useToast();
  const {
    settings,
    isLoading: settingsLoading,
    fetchSettings,
    updateSettings,
    runMemoryAnalysis,
    generatePerformanceReport
  } = useEvolutionSettings();

  const [selectedStatusComponent, setSelectedStatusComponent] = useState<string | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isPerformanceReportOpen, setIsPerformanceReportOpen] = useState(false);

  // Local state derived from settings
  const evolutionEnabled = settings?.evolution_enabled ?? true;
  const autoOptimization = settings?.auto_optimization ?? true;
  const systemStatus = settings?.system_status ?? {
    memoryEngine: "healthy",
    vectorStore: "healthy",
    evolutionService: "warning",
    promptSystem: "healthy"
  };

  // Mock metrics data
  const [metrics, setMetrics] = useState({
    learningEfficiency: 78,
    memoryQuality: 92,
    operationSuccess: 85,
    systemUptime: 99.7
  });

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const handleToggleEvolution = async (enabled: boolean) => {
    try {
      await updateSettings({ evolution_enabled: enabled });
      onSettingsChange(true);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleToggleAutoOptimization = async (enabled: boolean) => {
    try {
      await updateSettings({ auto_optimization: enabled });
      onSettingsChange(true);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleStatusClick = (component: string, status: string) => {
    if (status !== "healthy") {
      setSelectedStatusComponent(component);
      setIsStatusDialogOpen(true);
    }
  };

  const handleRunMemoryAnalysis = async () => {
    try {
      await runMemoryAnalysis();
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleGeneratePerformanceReport = async () => {
    setIsPerformanceReportOpen(true);
  };

  const handleViewEvolutionDashboard = () => {
    router.push('/evolution');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "text-green-400";
      case "warning": return "text-yellow-400";
      case "error": return "text-red-400";
      default: return "text-zinc-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return <CheckCircle className="h-4 w-4" />;
      case "warning": return <AlertTriangle className="h-4 w-4" />;
      case "error": return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-4 sm:space-y-6 overflow-x-hidden">
      {/* System Status Overview */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current status of evolution intelligence components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            {Object.entries(systemStatus).map(([component, status]) => (
              <div
                key={component}
                className={`flex items-center justify-between p-3 rounded-lg bg-zinc-800/50 transition-colors ${
                  status !== "healthy" ? "cursor-pointer hover:bg-zinc-700/50" : ""
                }`}
                onClick={() => handleStatusClick(component, status)}
                title={status !== "healthy" ? "Click for details and recovery options" : "System component is healthy"}
              >
                <div className="flex items-center gap-2">
                  <span className={getStatusColor(status)}>
                    {getStatusIcon(status)}
                  </span>
                  <span className="text-sm font-medium capitalize">
                    {component.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
                <Badge
                  variant={status === "healthy" ? "default" : status === "warning" ? "secondary" : "destructive"}
                  className="text-xs"
                >
                  {status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Metrics
          </CardTitle>
          <CardDescription>
            Current system performance indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            {Object.entries(metrics).map(([metric, value]) => (
              <div key={metric} className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="text-sm font-medium capitalize">
                    {metric.replace(/([A-Z])/g, ' $1').trim()}
                  </Label>
                  <span className="text-sm font-semibold">{value}%</span>
                </div>
                <Progress 
                  value={value} 
                  className="h-2"
                  // @ts-ignore
                  style={{
                    '--progress-background': value > 80 ? '#10b981' : value > 60 ? '#f59e0b' : '#ef4444'
                  }}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Evolution Control */}
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Evolution Intelligence
            </CardTitle>
            <CardDescription>
              Enable or disable memory evolution processing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="evolution-enabled" className="text-sm font-medium">
                  Enable Evolution
                </Label>
                <p className="text-xs text-zinc-400">
                  Process memory updates, conflicts, and optimizations
                </p>
              </div>
              <Switch
                id="evolution-enabled"
                checked={evolutionEnabled}
                onCheckedChange={handleToggleEvolution}
                disabled={settingsLoading}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="auto-optimization" className="text-sm font-medium">
                  Auto-Optimization
                </Label>
                <p className="text-xs text-zinc-400">
                  Automatically optimize memory structure and performance
                </p>
              </div>
              <Switch
                id="auto-optimization"
                checked={autoOptimization}
                onCheckedChange={handleToggleAutoOptimization}
                disabled={!evolutionEnabled || settingsLoading}
              />
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common configuration tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="flex-1 justify-start border-zinc-700 hover:bg-zinc-800"
                onClick={handleRunMemoryAnalysis}
                disabled={settingsLoading}
              >
                <Brain className="mr-2 h-4 w-4" />
                Run Memory Analysis
              </Button>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-1 h-8 w-8 hover:bg-zinc-700 hover:text-blue-400">
                    <Info className="h-4 w-4 text-zinc-300 hover:text-blue-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" className="max-w-sm">
                  <div className="space-y-2">
                    <p className="font-semibold">Memory Analysis</p>
                    <p className="text-sm">Performs a comprehensive analysis of your memory system including:</p>
                    <ul className="text-sm space-y-1 ml-2">
                      <li>• Memory quality assessment and scoring</li>
                      <li>• Duplicate detection and consolidation opportunities</li>
                      <li>• Relationship mapping between memories</li>
                      <li>• Performance bottleneck identification</li>
                      <li>• Storage optimization recommendations</li>
                    </ul>
                    <p className="text-sm text-zinc-400">Results are available in the reports section after completion.</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="flex-1 justify-start border-zinc-700 hover:bg-zinc-800"
                onClick={handleGeneratePerformanceReport}
                disabled={settingsLoading}
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                Generate Performance Report
              </Button>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-1 h-8 w-8 hover:bg-zinc-700 hover:text-blue-400">
                    <Info className="h-4 w-4 text-zinc-300 hover:text-blue-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" className="max-w-sm">
                  <div className="space-y-2">
                    <p className="font-semibold">Performance Report</p>
                    <p className="text-sm">Generates a comprehensive PDF report containing:</p>
                    <ul className="text-sm space-y-1 ml-2">
                      <li>• System performance metrics and trends</li>
                      <li>• Learning efficiency analysis (78% current)</li>
                      <li>• Memory quality scores (92% current)</li>
                      <li>• Operation success rates (85% current)</li>
                      <li>• System uptime statistics (99.7% current)</li>
                      <li>• Optimization recommendations</li>
                    </ul>
                    <p className="text-sm text-zinc-400">Report is automatically downloaded when ready.</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="flex-1 justify-start border-zinc-700 hover:bg-zinc-800"
                onClick={handleViewEvolutionDashboard}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                View Evolution Dashboard
              </Button>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-1 h-8 w-8 hover:bg-zinc-700 hover:text-blue-400">
                    <Info className="h-4 w-4 text-zinc-300 hover:text-blue-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left" className="max-w-sm">
                  <div className="space-y-2">
                    <p className="font-semibold">Evolution Dashboard</p>
                    <p className="text-sm">Opens the main evolution analytics dashboard featuring:</p>
                    <ul className="text-sm space-y-1 ml-2">
                      <li>• Real-time evolution timeline visualization</li>
                      <li>• Interactive operation breakdown charts</li>
                      <li>• Live activity feed with filtering</li>
                      <li>• Key metrics display with trends</li>
                      <li>• Export capabilities for data analysis</li>
                      <li>• Detailed operation history and insights</li>
                    </ul>
                    <p className="text-sm text-zinc-400">Navigate to /evolution for comprehensive analytics.</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Summary */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle>Current Configuration Summary</CardTitle>
          <CardDescription>
            Overview of active evolution settings across all domains
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium text-zinc-200">Domain Settings</h4>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-0 h-4 w-4 hover:bg-zinc-700">
                      <HelpCircle className="h-3 w-3 text-zinc-300 hover:text-blue-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-sm">
                    <div className="space-y-2">
                      <p className="font-semibold">Domain Configuration</p>
                      <p className="text-sm">Configure domain-specific memory processing rules:</p>
                      <ul className="text-sm space-y-1 ml-2">
                        <li>• <strong>Active Domains:</strong> Currently enabled memory domains</li>
                        <li>• <strong>Default Domain:</strong> Fallback domain for unclassified memories</li>
                        <li>• <strong>Custom Rules:</strong> Total domain-specific processing rules</li>
                      </ul>
                      <p className="text-sm text-zinc-400">Go to Domain tab to manage rules and settings.</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
              <ul className="space-y-1 text-zinc-400">
                <li>• Active Domains: 3</li>
                <li>• Default Domain: General</li>
                <li className="flex items-center gap-2">
                  • Custom Rules: 12
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" className="p-0 h-4 w-4 hover:bg-zinc-700">
                        <Info className="h-3 w-3 text-zinc-300 hover:text-blue-400" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="max-w-sm">
                      <div className="space-y-2">
                        <p className="font-semibold">Custom Rules (12 total)</p>
                        <p className="text-sm">Domain-specific processing rules:</p>
                        <ul className="text-sm space-y-1 ml-2">
                          <li>• <strong>General Knowledge (2):</strong> "Prioritize recent information", "Merge similar concepts"</li>
                          <li>• <strong>Technical Documentation (2):</strong> "Preserve version information", "Link related components"</li>
                          <li>• <strong>Personal Preferences (2):</strong> "Respect privacy settings", "Update preferences incrementally"</li>
                          <li>• <strong>Additional domains (6):</strong> Various custom rules</li>
                        </ul>
                        <p className="text-sm text-zinc-400 mt-2">
                          <strong>To manage rules:</strong> Go to Settings → Evolution → Domain tab → Select a domain → Add/remove rules in the Rules section.
                        </p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-zinc-200">Prompt Configuration</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• Fact Extraction: Custom</li>
                <li>• Memory Evolution: Default</li>
                <li>• Last Updated: 2 days ago</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-zinc-200">Advanced Settings</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• Batch Size: 50</li>
                <li>• Timeout: 30s</li>
                <li>• Retry Attempts: 3</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Status Dialog */}
      {selectedStatusComponent && (
        <SystemStatusDialog
          isOpen={isStatusDialogOpen}
          onClose={() => {
            setIsStatusDialogOpen(false);
            setSelectedStatusComponent(null);
          }}
          component={selectedStatusComponent}
          status={systemStatus[selectedStatusComponent as keyof typeof systemStatus]}
        />
      )}

      {/* Performance Report Dialog */}
      <PerformanceReportDialog
        isOpen={isPerformanceReportOpen}
        onClose={() => setIsPerformanceReportOpen(false)}
      />
      </div>
    </TooltipProvider>
  );
}

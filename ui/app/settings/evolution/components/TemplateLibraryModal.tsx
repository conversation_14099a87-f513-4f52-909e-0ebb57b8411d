"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  BookOpen, 
  Search, 
  Filter, 
  Download, 
  Eye,
  FileText,
  Code,
  Tag
} from "lucide-react";

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  category: "fact-extraction" | "memory-evolution" | "general";
  tags: string[];
}

interface TemplateLibraryModalProps {
  isOpen: boolean;
  onClose: () => void;
  templates: PromptTemplate[];
  onApplyTemplate: (template: PromptTemplate) => void;
}

export function TemplateLibraryModal({ 
  isO<PERSON>, 
  onClose, 
  templates, 
  onApplyTemplate 
}: TemplateLibraryModalProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [previewTemplate, setPreviewTemplate] = useState<PromptTemplate | null>(null);

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "fact-extraction": return "bg-blue-900/20 text-blue-400 border-blue-800";
      case "memory-evolution": return "bg-green-900/20 text-green-400 border-green-800";
      default: return "bg-zinc-900/20 text-zinc-400 border-zinc-800";
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "fact-extraction": return <FileText className="h-4 w-4" />;
      case "memory-evolution": return <Code className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] bg-zinc-900 border-zinc-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Prompt Template Library
          </DialogTitle>
          <DialogDescription>
            Browse and apply pre-built prompt templates for different use cases
          </DialogDescription>
        </DialogHeader>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-zinc-800 border-zinc-700"
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
              className="border-zinc-700"
            >
              All
            </Button>
            <Button
              variant={selectedCategory === "fact-extraction" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("fact-extraction")}
              className="border-zinc-700"
            >
              <FileText className="mr-1 h-3 w-3" />
              Fact Extraction
            </Button>
            <Button
              variant={selectedCategory === "memory-evolution" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("memory-evolution")}
              className="border-zinc-700"
            >
              <Code className="mr-1 h-3 w-3" />
              Memory Evolution
            </Button>
          </div>
        </div>

        {/* Template Grid */}
        <ScrollArea className="h-[400px] pr-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredTemplates.map((template) => (
              <Card key={template.id} className="bg-zinc-800/50 border-zinc-700 hover:border-zinc-600 transition-colors">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(template.category)}
                      <CardTitle className="text-sm">{template.name}</CardTitle>
                    </div>
                    <Badge className={getCategoryColor(template.category)}>
                      {template.category}
                    </Badge>
                  </div>
                  <CardDescription className="text-xs">
                    {template.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-3">
                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {template.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs border-zinc-600">
                        <Tag className="mr-1 h-2 w-2" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => setPreviewTemplate(template)}
                      variant="outline"
                      className="flex-1 border-zinc-700"
                    >
                      <Eye className="mr-1 h-3 w-3" />
                      Preview
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => onApplyTemplate(template)}
                      className="flex-1 bg-primary hover:bg-primary/90"
                    >
                      <Download className="mr-1 h-3 w-3" />
                      Apply
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {filteredTemplates.length === 0 && (
            <div className="text-center py-8 text-zinc-400">
              <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No templates found matching your criteria</p>
            </div>
          )}
        </ScrollArea>

        {/* Template Preview Modal */}
        {previewTemplate && (
          <Dialog open={!!previewTemplate} onOpenChange={() => setPreviewTemplate(null)}>
            <DialogContent className="max-w-3xl max-h-[80vh] bg-zinc-900 border-zinc-800">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {getCategoryIcon(previewTemplate.category)}
                  {previewTemplate.name}
                </DialogTitle>
                <DialogDescription>
                  {previewTemplate.description}
                </DialogDescription>
              </DialogHeader>
              
              <ScrollArea className="h-[400px] pr-4">
                <pre className="text-sm bg-zinc-800 p-4 rounded-lg whitespace-pre-wrap font-mono">
                  {previewTemplate.content}
                </pre>
              </ScrollArea>
              
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setPreviewTemplate(null)}
                  className="border-zinc-700"
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    onApplyTemplate(previewTemplate);
                    setPreviewTemplate(null);
                  }}
                  className="bg-primary hover:bg-primary/90"
                >
                  Apply Template
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
}

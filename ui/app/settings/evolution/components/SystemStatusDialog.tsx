"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  RefreshCw,
  ExternalLink,
  Terminal,
  Database,
  Brain,
  FileText
} from "lucide-react";

interface SystemStatusDialogProps {
  isOpen: boolean;
  onClose: () => void;
  component: string;
  status: string;
}

export function SystemStatusDialog({ isOpen, onClose, component, status }: SystemStatusDialogProps) {
  const [isRecovering, setIsRecovering] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "warning": return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case "error": return <XCircle className="h-5 w-5 text-red-400" />;
      default: return <AlertTriangle className="h-5 w-5 text-zinc-400" />;
    }
  };

  const getComponentIcon = (component: string) => {
    switch (component) {
      case "memoryEngine": return <Brain className="h-5 w-5" />;
      case "vectorStore": return <Database className="h-5 w-5" />;
      case "evolutionService": return <RefreshCw className="h-5 w-5" />;
      case "promptSystem": return <FileText className="h-5 w-5" />;
      default: return <Terminal className="h-5 w-5" />;
    }
  };

  const getComponentDisplayName = (component: string) => {
    switch (component) {
      case "memoryEngine": return "Memory Engine";
      case "vectorStore": return "Vector Store";
      case "evolutionService": return "Evolution Service";
      case "promptSystem": return "Prompt System";
      default: return component;
    }
  };

  const getStatusDetails = (component: string, status: string) => {
    const details = {
      memoryEngine: {
        healthy: {
          description: "Memory engine is operating normally with all core functions available.",
          details: [
            "✓ Memory storage and retrieval working",
            "✓ Embedding generation functional",
            "✓ Search operations responding normally",
            "✓ All API endpoints accessible"
          ],
          actions: []
        },
        warning: {
          description: "Memory engine is experiencing minor issues that may affect performance.",
          details: [
            "⚠ Slower than normal response times detected",
            "⚠ Some embedding operations taking longer",
            "⚠ Memory quality scores below optimal range",
            "✓ Core functionality still available"
          ],
          actions: [
            "Check system resources and memory usage",
            "Review recent memory operations for errors",
            "Consider restarting the memory service",
            "Monitor performance metrics closely"
          ]
        },
        error: {
          description: "Memory engine is experiencing critical issues requiring immediate attention.",
          details: [
            "✗ Memory storage operations failing",
            "✗ Embedding service unreachable",
            "✗ Search functionality degraded",
            "✗ Multiple API endpoints returning errors"
          ],
          actions: [
            "Restart the memory service immediately",
            "Check database connectivity",
            "Verify embedding service configuration",
            "Review system logs for error details",
            "Contact system administrator if issues persist"
          ]
        }
      },
      vectorStore: {
        healthy: {
          description: "Vector store (Qdrant) is connected and functioning properly.",
          details: [
            "✓ Database connection established",
            "✓ Vector operations responding normally",
            "✓ Index integrity maintained",
            "✓ Search performance optimal"
          ],
          actions: []
        },
        warning: {
          description: "Vector store is accessible but showing performance degradation.",
          details: [
            "⚠ Connection latency higher than normal",
            "⚠ Some vector operations timing out",
            "⚠ Index fragmentation detected",
            "✓ Core search functionality available"
          ],
          actions: [
            "Check network connectivity to Qdrant",
            "Monitor vector store resource usage",
            "Consider rebuilding vector indexes",
            "Review Qdrant configuration settings"
          ]
        },
        error: {
          description: "Vector store is unreachable or experiencing critical failures.",
          details: [
            "✗ Cannot connect to Qdrant instance",
            "✗ Vector operations failing",
            "✗ Search functionality unavailable",
            "✗ Data integrity concerns"
          ],
          actions: [
            "Verify Qdrant service is running",
            "Check network connectivity (memory-qdrant:6333)",
            "Restart Qdrant container if necessary",
            "Verify database credentials and permissions",
            "Check disk space and system resources"
          ]
        }
      },
      evolutionService: {
        healthy: {
          description: "Evolution service is processing memory updates efficiently.",
          details: [
            "✓ Memory evolution operations running",
            "✓ Conflict resolution working properly",
            "✓ Quality scoring functional",
            "✓ Analytics data being collected"
          ],
          actions: []
        },
        warning: {
          description: "Evolution service is running but with reduced efficiency.",
          details: [
            "⚠ Processing queue building up",
            "⚠ Some evolution operations failing",
            "⚠ Quality scores inconsistent",
            "✓ Basic functionality still available"
          ],
          actions: [
            "Check evolution service logs for errors",
            "Review custom prompt configurations",
            "Monitor processing queue status",
            "Verify mem0 client configuration"
          ]
        },
        error: {
          description: "Evolution service is not functioning and requires immediate attention.",
          details: [
            "✗ Evolution operations completely stopped",
            "✗ Memory updates not being processed",
            "✗ Analytics data collection failed",
            "✗ Service may be crashed or misconfigured"
          ],
          actions: [
            "Restart evolution service immediately",
            "Check mem0 client initialization",
            "Verify custom prompt configurations",
            "Review service dependencies and connections",
            "Check for version compatibility issues"
          ]
        }
      },
      promptSystem: {
        healthy: {
          description: "Custom prompt system is loaded and functioning correctly.",
          details: [
            "✓ Custom prompts loaded successfully",
            "✓ Fact extraction prompts active",
            "✓ Memory evolution prompts configured",
            "✓ Domain-specific rules applied"
          ],
          actions: []
        },
        warning: {
          description: "Prompt system is working but may have configuration issues.",
          details: [
            "⚠ Some custom prompts not loading",
            "⚠ Default prompts being used as fallback",
            "⚠ Prompt validation warnings detected",
            "✓ Core prompt functionality available"
          ],
          actions: [
            "Review custom prompt configurations",
            "Check prompt syntax and formatting",
            "Verify prompt file permissions",
            "Test prompt validation system"
          ]
        },
        error: {
          description: "Prompt system has failed to load or is severely misconfigured.",
          details: [
            "✗ Custom prompts failed to load",
            "✗ Prompt validation system not working",
            "✗ Default prompts may be corrupted",
            "✗ Evolution intelligence severely impacted"
          ],
          actions: [
            "Reset prompt configurations to defaults",
            "Check prompt file integrity",
            "Verify mem0 version compatibility (v1.1+ required)",
            "Reload prompt system configuration",
            "Contact administrator for prompt restoration"
          ]
        }
      }
    };

    return details[component as keyof typeof details]?.[status as keyof typeof details.memoryEngine] || {
      description: "Status information not available for this component.",
      details: ["Status details could not be retrieved"],
      actions: ["Contact system administrator for assistance"]
    };
  };

  const handleRecoveryAction = async () => {
    setIsRecovering(true);
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";
      const response = await fetch(`${API_URL}/api/v1/health/recovery/${component}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Recovery request failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log(`Recovery initiated for ${component}: ${result.message}`);
        // You could add a toast notification here
      } else {
        throw new Error(result.message || 'Recovery failed');
      }
    } catch (error) {
      console.error("Recovery action failed:", error);
      // You could add error toast notification here
    } finally {
      setIsRecovering(false);
    }
  };

  const statusDetails = getStatusDetails(component, status);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-zinc-900 border-zinc-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {getComponentIcon(component)}
            {getComponentDisplayName(component)} Status
            {getStatusIcon(status)}
          </DialogTitle>
          <DialogDescription>
            Detailed status information and recovery instructions
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Overview */}
          <Card className="bg-zinc-800/50 border-zinc-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center justify-between">
                Current Status
                <Badge 
                  variant={status === "healthy" ? "default" : status === "warning" ? "secondary" : "destructive"}
                  className="text-xs"
                >
                  {status.toUpperCase()}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-zinc-300">{statusDetails.description}</p>
            </CardContent>
          </Card>

          {/* Status Details */}
          <Card className="bg-zinc-800/50 border-zinc-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">System Details</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {statusDetails.details.map((detail, index) => (
                  <li key={index} className="text-sm text-zinc-300 font-mono">
                    {detail}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Recovery Actions */}
          {statusDetails.actions.length > 0 && (
            <Card className="bg-zinc-800/50 border-zinc-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Recovery Actions</CardTitle>
                <CardDescription>
                  Follow these steps to resolve the issue
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ol className="space-y-2 mb-4">
                  {statusDetails.actions.map((action, index) => (
                    <li key={index} className="text-sm text-zinc-300 flex items-start gap-2">
                      <span className="text-zinc-500 font-mono text-xs mt-0.5">
                        {index + 1}.
                      </span>
                      {action}
                    </li>
                  ))}
                </ol>
                
                {status !== "healthy" && (
                  <div className="flex gap-2">
                    <Button
                      onClick={handleRecoveryAction}
                      disabled={isRecovering}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isRecovering ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Recovering...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Attempt Auto-Recovery
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";
                        window.open(`${API_URL}/api/v1/health`, '_blank');
                      }}
                      className="border-zinc-600"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View System Health
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

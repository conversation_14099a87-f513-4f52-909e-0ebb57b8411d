import { useState, useCallback, useEffect } from 'react';
import axios from 'axios';

export interface MetricTrend {
  direction: 'up' | 'down' | 'neutral';
  percentage: number;
  period: string;
}

export interface KeyMetric {
  value: string;
  trend: MetricTrend;
  description: string;
}

export interface KeyMetricsData {
  learning_efficiency: KeyMetric;
  conflict_resolution: KeyMetric;
  memory_quality: KeyMetric;
  operation_distribution: KeyMetric;
}

export interface UseKeyMetricsReturn {
  metrics: KeyMetricsData | null;
  isLoading: boolean;
  error: string | null;
  fetchMetrics: (timeframe?: 'day' | 'week' | 'month' | 'year') => Promise<void>;
  refetch: () => Promise<void>;
}

export const useKeyMetrics = (
  initialTimeframe: 'day' | 'week' | 'month' | 'year' = 'week',
  autoFetch: boolean = true
): UseKeyMetricsReturn => {
  const [metrics, setMetrics] = useState<KeyMetricsData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTimeframe, setCurrentTimeframe] = useState<'day' | 'week' | 'month' | 'year'>(initialTimeframe);
  
  const URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";

  const fetchMetrics = useCallback(async (timeframe: 'day' | 'week' | 'month' | 'year' = currentTimeframe): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.get<KeyMetricsData>(
        `${URL}/api/v1/evolution-config/metrics?timeframe=${timeframe}`
      );
      
      setMetrics(response.data);
      setCurrentTimeframe(timeframe);
      setIsLoading(false);
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch key metrics';
      setError(errorMessage);
      setIsLoading(false);
      console.error('Error fetching key metrics:', err);
    }
  }, [URL, currentTimeframe]);

  const refetch = useCallback(async (): Promise<void> => {
    await fetchMetrics(currentTimeframe);
  }, [fetchMetrics, currentTimeframe]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchMetrics(initialTimeframe);
    }
  }, [autoFetch, initialTimeframe, fetchMetrics]);

  return {
    metrics,
    isLoading,
    error,
    fetchMetrics,
    refetch
  };
};

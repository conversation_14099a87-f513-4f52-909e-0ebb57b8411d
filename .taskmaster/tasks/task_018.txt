# Task ID: 18
# Title: Build Guided Setup Wizard
# Status: pending
# Dependencies: 17
# Priority: medium
# Description: Create onboarding wizard for new users with automatic role detection and domain configuration
# Details:
Create SetupWizard component with multi-step flow: role detection, domain selection, initial configuration, and validation testing. Implement automatic domain suggestions based on user role, guided prompt customization with templates, and initial testing with sample scenarios. Add progress tracking and ability to skip/return to wizard steps.

# Test Strategy:
Test wizard flow completion, verify role detection accuracy, test domain suggestions, and validate initial configuration setup

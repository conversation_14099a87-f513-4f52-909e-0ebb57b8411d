# Task ID: 9
# Title: Implement Custom Prompt Editor Interface
# Status: done
# Dependencies: 8
# Priority: high
# Description: Build dual-prompt editor for Fact Extraction and Memory Evolution prompts with validation and version control
# Details:
Create PromptEditor component with two side-by-side editors using Monaco Editor or CodeMirror. Implement syntax highlighting, line numbers, and 4000-character limits with real-time counters. Add auto-save every 30 seconds, version history tracking, and reset to defaults functionality. Include real-time validation for prompt structure, required sections, and JSON output format. Add import/export capabilities.

# Test Strategy:
Test editor functionality and validation, verify auto-save and version history, test character limits and reset functionality, and validate import/export features

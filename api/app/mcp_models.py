"""
MCP Request/Response Models for OpenMemory

This module contains Pydantic models for MCP request/response validation,
error handling, and type safety throughout the system.

These models provide:
- Input validation for MCP tool parameters
- Structured response formats
- Error handling with consistent error types
- Type safety for all MCP operations

Separated from SQLAlchemy models to provide clean API layer validation.
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator, ConfigDict
from datetime import datetime
from enum import Enum


class MCPErrorType(str, Enum):
    """Standard error types for MCP operations."""
    VALIDATION_ERROR = "validation_error"
    SYSTEM_UNAVAILABLE = "system_unavailable"
    APP_PAUSED = "app_paused"
    PERMISSION_DENIED = "permission_denied"
    OPERATION_FAILED = "operation_failed"
    CHUNKING_FAILED = "chunking_failed"
    TRANSACTION_FAILED = "transaction_failed"
    INTERNAL_ERROR = "internal_error"


class MCPResponse(BaseModel):
    """Base response model for all MCP operations."""
    success: bool
    message: str
    error_type: Optional[MCPErrorType] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    
    model_config = ConfigDict(use_enum_values=True)


class AddMemoryRequest(BaseModel):
    """Request model for adding memories."""
    text: str = Field(..., min_length=1, max_length=50000, description="Text content to store as memory")
    
    @field_validator('text')
    @classmethod
    def validate_text_content(cls, v):
        if not v.strip():
            raise ValueError("Text content cannot be empty or only whitespace")
        return v.strip()


class MemoryChunkInfo(BaseModel):
    """Information about a memory chunk."""
    chunk_index: int
    chunk_part: str
    original_length: int
    chunk_length: int


class ProcessedMemory(BaseModel):
    """Information about a processed memory."""
    id: str
    event: str
    content: str
    is_chunk: bool = False
    chunk_info: Optional[MemoryChunkInfo] = None


class AddMemoryResponse(MCPResponse):
    """Response model for adding memories."""
    processed_memories: Optional[List[ProcessedMemory]] = None
    transaction_id: Optional[str] = None
    chunks_count: Optional[int] = None
    is_chunked: bool = False
    degraded_mode: bool = False


class SearchMemoryRequest(BaseModel):
    """Request model for searching memories."""
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    limit: int = Field(default=10, ge=1, le=100, description="Maximum number of results to return")
    
    @field_validator('query')
    @classmethod
    def validate_query_content(cls, v):
        if not v.strip():
            raise ValueError("Search query cannot be empty or only whitespace")
        return v.strip()


class MemorySearchResult(BaseModel):
    """A single memory search result."""
    id: str
    memory: str
    score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    hash: Optional[str] = None


class SearchMemoryResponse(MCPResponse):
    """Response model for searching memories."""
    results: Optional[List[MemorySearchResult]] = None
    results_count: int = 0
    query: Optional[str] = None


class ListMemoriesRequest(BaseModel):
    """Request model for listing memories."""
    # No additional parameters needed for basic listing
    pass


class MemoryListItem(BaseModel):
    """A single memory item in the list."""
    id: str
    memory: str
    metadata: Optional[Dict[str, Any]] = None
    hash: Optional[str] = None
    created_at: Optional[str] = None


class ListMemoriesResponse(MCPResponse):
    """Response model for listing memories."""
    memories: Optional[List[MemoryListItem]] = None
    total_count: int = 0


class HealthStatus(BaseModel):
    """System health status information."""
    status: str
    degraded_mode: bool
    client_healthy: bool
    timestamp: str
    active_operations: int = 0
    total_operations: int = 0
    overall_success_rate: float = 0.0


class DegradationInfo(BaseModel):
    """Degradation status information."""
    degraded_mode: bool
    vector_store_available: bool
    fallback_mode_active: bool
    degradation_reason: Optional[str] = None
    queue_size: int = 0


class OperationStats(BaseModel):
    """Statistics for a specific operation type."""
    total_count: int
    success_count: int
    failure_count: int
    retry_count: int
    avg_duration_ms: Optional[float] = None
    min_duration_ms: Optional[int] = None
    max_duration_ms: Optional[int] = None
    last_operation: Optional[float] = None
    error_types: Dict[str, int] = Field(default_factory=dict)
    performance_distribution: Dict[str, int] = Field(default_factory=dict)


class SystemHealthResponse(MCPResponse):
    """Response model for system health status."""
    health_status: Optional[HealthStatus] = None
    degradation: Optional[DegradationInfo] = None
    recommendations: List[str] = Field(default_factory=list)
    raw_data: Optional[Dict[str, Any]] = None


class ActiveOperation(BaseModel):
    """Information about an active operation."""
    operation_id: str
    operation_type: str
    user_id: Optional[str] = None
    client_name: Optional[str] = None
    start_time: float
    duration_ms: int
    is_long_running: bool = False


class OperationMetricsResponse(MCPResponse):
    """Response model for operation metrics."""
    metrics_timestamp: float
    active_operations_count: int
    active_operations: List[ActiveOperation] = Field(default_factory=list)
    operations: Dict[str, OperationStats] = Field(default_factory=dict)
    raw_data: Optional[Dict[str, Any]] = None


class MCPErrorResponse(MCPResponse):
    """Standardized error response for MCP operations."""
    success: bool = False
    error_details: Optional[Dict[str, Any]] = None
    
    @classmethod
    def create(cls, error_type: MCPErrorType, message: str, details: Optional[Dict[str, Any]] = None):
        """Create a standardized error response."""
        return cls(
            error_type=error_type,
            message=message,
            error_details=details
        )


class ValidationError(BaseModel):
    """Validation error details."""
    field: str
    message: str
    invalid_value: Any


class MCPValidationErrorResponse(MCPErrorResponse):
    """Validation error response with field-specific details."""
    error_type: MCPErrorType = MCPErrorType.VALIDATION_ERROR
    validation_errors: List[ValidationError] = Field(default_factory=list)


# Utility functions for creating responses

def create_success_response(message: str, **kwargs) -> MCPResponse:
    """Create a successful MCP response."""
    return MCPResponse(success=True, message=message, **kwargs)


def create_error_response(error_type: MCPErrorType, message: str, **kwargs) -> MCPErrorResponse:
    """Create an error MCP response."""
    return MCPErrorResponse.create(error_type, message, kwargs.get('details'))


def create_add_memory_success(message: str, processed_memories: List[ProcessedMemory], 
                             is_chunked: bool = False, transaction_id: Optional[str] = None,
                             degraded_mode: bool = False) -> AddMemoryResponse:
    """Create a successful add memory response."""
    return AddMemoryResponse(
        success=True,
        message=message,
        processed_memories=processed_memories,
        is_chunked=is_chunked,
        chunks_count=len(processed_memories) if is_chunked else 1,
        transaction_id=transaction_id,
        degraded_mode=degraded_mode
    )


def create_search_memory_success(message: str, results: List[MemorySearchResult], 
                                query: str) -> SearchMemoryResponse:
    """Create a successful search memory response."""
    return SearchMemoryResponse(
        success=True,
        message=message,
        results=results,
        results_count=len(results),
        query=query
    )


def create_list_memories_success(message: str, memories: List[MemoryListItem]) -> ListMemoriesResponse:
    """Create a successful list memories response."""
    return ListMemoriesResponse(
        success=True,
        message=message,
        memories=memories,
        total_count=len(memories)
    )


def create_health_response(success: bool, message: str, health_data: Dict[str, Any]) -> SystemHealthResponse:
    """Create a system health response."""
    if not success:
        return SystemHealthResponse(
            success=False,
            message=message,
            error_type=MCPErrorType.SYSTEM_UNAVAILABLE
        )
    
    # Extract health status information
    health_status = HealthStatus(
        status=health_data.get("overall_health", "unknown"),
        degraded_mode=health_data.get("degradation", {}).get("degraded_mode", False),
        client_healthy=health_data.get("client_healthy", False),
        timestamp=health_data.get("timestamp", datetime.now().isoformat())
    )
    
    # Extract degradation information
    degradation_data = health_data.get("degradation", {})
    degradation = DegradationInfo(
        degraded_mode=degradation_data.get("degraded_mode", False),
        vector_store_available=degradation_data.get("vector_store_available", True),
        fallback_mode_active=degradation_data.get("fallback_mode_active", False),
        degradation_reason=degradation_data.get("degradation_reason"),
        queue_size=degradation_data.get("queue_size", 0)
    )
    
    return SystemHealthResponse(
        success=True,
        message=message,
        health_status=health_status,
        degradation=degradation,
        recommendations=health_data.get("recommendations", []),
        raw_data=health_data
    )


def create_metrics_response(success: bool, message: str, metrics_data: Dict[str, Any]) -> OperationMetricsResponse:
    """Create an operation metrics response."""
    if not success:
        return OperationMetricsResponse(
            success=False,
            message=message,
            error_type=MCPErrorType.SYSTEM_UNAVAILABLE,
            metrics_timestamp=datetime.now().timestamp(),
            active_operations_count=0
        )
    
    metrics = metrics_data.get("metrics", {})
    active_ops_data = metrics_data.get("active_operations", {})
    
    # Convert active operations
    active_operations = []
    for op_id, op_info in active_ops_data.items():
        active_operations.append(ActiveOperation(
            operation_id=op_id,
            operation_type=op_info.get("operation_type", "unknown"),
            user_id=op_info.get("user_id"),
            client_name=op_info.get("client_name"),
            start_time=op_info.get("start_time", 0),
            duration_ms=op_info.get("duration_ms", 0),
            is_long_running=op_info.get("is_long_running", False)
        ))
    
    # Convert operation statistics
    operations = {}
    for op_type, stats in metrics.get("operations", {}).items():
        operations[op_type] = OperationStats(**stats)
    
    return OperationMetricsResponse(
        success=True,
        message=message,
        metrics_timestamp=metrics.get("metrics_timestamp", datetime.now().timestamp()),
        active_operations_count=metrics.get("active_operations_count", 0),
        active_operations=active_operations,
        operations=operations,
        raw_data=metrics_data
    )

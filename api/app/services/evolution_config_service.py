"""
Evolution Configuration Service

Handles persistence and management of evolution intelligence settings.
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class EvolutionConfigService:
    """Service for managing evolution intelligence configuration."""
    
    def __init__(self, config_dir: str = "/app/data/config"):
        self.config_dir = Path(config_dir)
        self.config_file = self.config_dir / "evolution_settings.json"
        self.backup_dir = self.config_dir / "backups"
        
        # Ensure directories exist
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Default configuration
        self.default_config = {
            "evolution_enabled": True,
            "auto_optimization": True,
            "system_status": {
                "memory_engine": "healthy",
                "vector_store": "healthy",
                "evolution_service": "warning",  # Intentional for demo
                "prompt_system": "healthy"
            },
            "advanced_settings": {
                "processing_interval_seconds": 30,
                "batch_size": 10,
                "quality_threshold": 0.8,
                "conflict_resolution_strategy": "merge",
                "auto_backup_enabled": True,
                "max_queue_size": 1000
            },
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        }
    
    def get_settings(self) -> Dict[str, Any]:
        """Get current evolution settings."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    
                # Merge with defaults to ensure all keys exist
                merged_config = self._merge_with_defaults(config)
                return merged_config
            else:
                # Create default config file
                self._save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            logger.error(f"Error loading evolution settings: {e}")
            return self.default_config.copy()
    
    def update_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update evolution settings and persist to disk."""
        try:
            # Get current settings
            current_config = self.get_settings()
            
            # Create backup before updating
            self._create_backup(current_config)
            
            # Update only the provided fields
            updated_config = self._deep_update(current_config, new_settings)
            
            # Update metadata
            updated_config["metadata"]["updated_at"] = datetime.now().isoformat()
            
            # Save to disk
            self._save_config(updated_config)
            
            logger.info(f"Evolution settings updated: {new_settings}")
            return updated_config
            
        except Exception as e:
            logger.error(f"Error updating evolution settings: {e}")
            raise
    
    def reset_to_defaults(self) -> Dict[str, Any]:
        """Reset settings to default values."""
        try:
            # Create backup before resetting
            current_config = self.get_settings()
            self._create_backup(current_config)
            
            # Reset to defaults
            default_config = self.default_config.copy()
            default_config["metadata"]["updated_at"] = datetime.now().isoformat()
            
            self._save_config(default_config)
            
            logger.info("Evolution settings reset to defaults")
            return default_config
            
        except Exception as e:
            logger.error(f"Error resetting evolution settings: {e}")
            raise
    
    def get_system_status(self) -> Dict[str, str]:
        """Get current system component status."""
        settings = self.get_settings()
        return settings.get("system_status", {})
    
    def update_system_status(self, component: str, status: str) -> Dict[str, Any]:
        """Update status of a specific system component."""
        try:
            current_config = self.get_settings()
            
            if "system_status" not in current_config:
                current_config["system_status"] = {}
            
            current_config["system_status"][component] = status
            current_config["metadata"]["updated_at"] = datetime.now().isoformat()
            
            self._save_config(current_config)
            
            logger.info(f"System status updated: {component} = {status}")
            return current_config
            
        except Exception as e:
            logger.error(f"Error updating system status: {e}")
            raise
    
    def _save_config(self, config: Dict[str, Any]) -> None:
        """Save configuration to disk."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving config to {self.config_file}: {e}")
            raise
    
    def _create_backup(self, config: Dict[str, Any]) -> None:
        """Create a backup of the current configuration."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"evolution_settings_{timestamp}.json"
            
            with open(backup_file, 'w') as f:
                json.dump(config, f, indent=2, default=str)
                
            # Keep only last 10 backups
            self._cleanup_old_backups()
            
        except Exception as e:
            logger.warning(f"Error creating backup: {e}")
    
    def _cleanup_old_backups(self, keep_count: int = 10) -> None:
        """Remove old backup files, keeping only the most recent ones."""
        try:
            backup_files = list(self.backup_dir.glob("evolution_settings_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for old_backup in backup_files[keep_count:]:
                old_backup.unlink()
                
        except Exception as e:
            logger.warning(f"Error cleaning up old backups: {e}")
    
    def _merge_with_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge loaded config with defaults to ensure all keys exist."""
        merged = self.default_config.copy()
        return self._deep_update(merged, config)
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively update nested dictionaries."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                base_dict[key] = self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
        return base_dict
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get evolution processing statistics."""
        settings = self.get_settings()
        
        # Mock processing stats for demonstration
        # In a real implementation, this would come from actual processing metrics
        return {
            "total_operations_today": 247,
            "successful_operations": 234,
            "failed_operations": 13,
            "success_rate": 94.7,
            "average_processing_time_ms": 156,
            "queue_size": 15 if settings.get("evolution_enabled") else 0,
            "auto_optimization_active": settings.get("auto_optimization", False),
            "last_optimization": "2024-01-15T14:30:00Z" if settings.get("auto_optimization") else None,
            "optimizations_performed_today": 8 if settings.get("auto_optimization") else 0
        }


# Global instance
_evolution_config_service = None

def get_evolution_config_service() -> EvolutionConfigService:
    """Get the global evolution config service instance."""
    global _evolution_config_service
    if _evolution_config_service is None:
        _evolution_config_service = EvolutionConfigService()
    return _evolution_config_service

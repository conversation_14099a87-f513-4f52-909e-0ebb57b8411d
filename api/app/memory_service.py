"""
Memory Service Layer for OpenMemory

This module contains all core memory operations including:
- Memory addition with chunking logic
- Memory search functionality  
- Memory listing operations
- Transaction handling for chunked operations
- Memory validation and verification

Separated from MCP server to provide clean business logic layer.
"""

import logging
import uuid
import datetime
from typing import List, Tuple, Any, Optional, Dict
from sqlalchemy.orm import Session

from app.database import <PERSON>Loc<PERSON>
from app.models import Memory, MemoryState, MemoryStatusHistory, User, App
from app.utils.db import get_user_and_app
from app.utils.memory import get_memory_client, MemoryClientSingleton
from app.enhanced_logging import log_memory_operation, operation_logger


class MemoryService:
    """Core memory operations service."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_memory_client_safe(self):
        """Get memory client with error handling and retry. Returns None if client cannot be initialized."""
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                client = get_memory_client()
                if client is not None:
                    return client
                else:
                    self.logger.warning(f"Memory client returned None on attempt {attempt + 1}")
            except Exception as e:
                self.logger.warning(f"Failed to get memory client (attempt {attempt + 1}): {e}")
                if attempt < max_attempts - 1:
                    import time
                    time.sleep(0.5)  # Brief delay before retry

        self.logger.error("Failed to get memory client after all attempts")
        return None

    def get_memory_singleton_safe(self):
        """Get memory client singleton with error handling and retry. Returns None if client cannot be initialized."""
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                singleton = MemoryClientSingleton()
                if singleton is not None:
                    # CRITICAL FIX: Check if the singleton has a valid client
                    # This prevents the race condition where singleton exists but client is None
                    if hasattr(singleton, '_client') and singleton._client is not None:
                        return singleton
                    else:
                        self.logger.warning(f"Memory singleton has no client on attempt {attempt + 1} - initialization race condition")
                        # Try to trigger client initialization
                        try:
                            from app.utils.memory import get_memory_client
                            client = get_memory_client()
                            if client is not None:
                                return singleton
                        except Exception as init_error:
                            self.logger.warning(f"Failed to initialize client: {init_error}")
                else:
                    self.logger.warning(f"Memory singleton returned None on attempt {attempt + 1}")
            except Exception as e:
                self.logger.warning(f"Failed to get memory client singleton (attempt {attempt + 1}): {e}")
                if attempt < max_attempts - 1:
                    import time
                    time.sleep(0.5)  # Brief delay before retry

        self.logger.error("Failed to get memory client singleton after all attempts")
        return None

    def get_max_text_length_from_config(self) -> int:
        """Get max_text_length from configuration, fallback to default if not available."""
        try:
            from app.models import Config as ConfigModel
            
            db = SessionLocal()
            try:
                config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
                if config and "openmemory" in config.value and "max_text_length" in config.value["openmemory"]:
                    return config.value["openmemory"]["max_text_length"]
            finally:
                db.close()
        except Exception as e:
            self.logger.warning(f"Error getting max_text_length from config: {e}")
        
        # Fallback to default
        return 2000

    def validate_text_length(self, text: str) -> Tuple[bool, str]:
        """Validate text length for memory storage."""
        max_length = self.get_max_text_length_from_config()
        if len(text) > max_length:
            return False, f"Warning: Text is {len(text)} characters (over {max_length} limit). Will be automatically chunked for processing."
        return True, "Text length valid"

    def chunk_text(self, text: str, max_length: int = None) -> List[str]:
        """
        Chunk text into smaller pieces for reliable memory storage.
        Implements smart chunking that preserves sentence boundaries when possible.
        """
        if max_length is None:
            max_length = self.get_max_text_length_from_config()
        
        # If text is already short enough, return as-is
        if len(text) <= max_length:
            return [text]
        
        self.logger.info(f"CHUNKING: Text length {len(text)} exceeds limit {max_length}, chunking...")
        
        chunks = []
        current_pos = 0
        
        while current_pos < len(text):
            # Calculate chunk end position
            chunk_end = min(current_pos + max_length, len(text))
            
            # If this is not the last chunk, try to break at sentence boundary
            if chunk_end < len(text):
                # Look for sentence endings within the last 200 chars of the chunk
                search_start = max(current_pos, chunk_end - 200)
                sentence_endings = []
                
                for i in range(search_start, chunk_end):
                    if text[i] in '.!?':
                        # Check if this is likely a sentence ending (not abbreviation)
                        if i + 1 < len(text) and text[i + 1] in ' \n\t':
                            sentence_endings.append(i + 1)
                
                # Use the last sentence ending found, or fall back to word boundary
                if sentence_endings:
                    chunk_end = sentence_endings[-1]
                else:
                    # Look for word boundary within last 100 chars
                    search_start = max(current_pos, chunk_end - 100)
                    word_boundaries = []
                    
                    for i in range(search_start, chunk_end):
                        if text[i] in ' \n\t':
                            word_boundaries.append(i)
                    
                    if word_boundaries:
                        chunk_end = word_boundaries[-1]
            
            # Extract the chunk
            chunk = text[current_pos:chunk_end].strip()
            if chunk:  # Only add non-empty chunks
                chunks.append(chunk)
                self.logger.info(f"CHUNKING: Created chunk {len(chunks)} with length {len(chunk)}")
            
            current_pos = chunk_end
        
        self.logger.info(f"CHUNKING: Split {len(text)} chars into {len(chunks)} chunks")
        return chunks

    def add_memory(self, text: str, user_id: str, client_name: str, request_id: Optional[int] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        Add a memory to the system with automatic chunking if needed.
        
        Returns:
            Tuple[bool, str, Optional[Dict]]: (success, message, result_data)
        """
        start_time = datetime.datetime.now()
        
        # Validate inputs
        if not user_id:
            return False, "Error: user_id not provided", None
        if not client_name:
            return False, "Error: client_name not provided", None
        if not text.strip():
            return False, "Error: empty text provided", None
        
        # Check text length and determine if chunking is needed
        max_length = self.get_max_text_length_from_config()
        needs_chunking = len(text) > max_length
        
        if needs_chunking:
            return self._add_memory_chunked(text, user_id, client_name, request_id)
        else:
            return self._add_memory_single(text, user_id, client_name, request_id)

    def _add_memory_single(self, text: str, user_id: str, client_name: str, request_id: Optional[int] = None) -> Tuple[bool, str, Optional[Dict]]:
        """Add a single memory without chunking."""
        # Get memory client singleton for degradation support
        memory_singleton = self.get_memory_singleton_safe()
        if not memory_singleton:
            return False, "Error: Memory system is currently unavailable. Please try again later.", None

        try:
            db = SessionLocal()
            try:
                # Get or create user and app
                user, app = get_user_and_app(db, user_id=user_id, app_id=client_name)
                
                # Check if app is active
                if not app.is_active:
                    return False, f"Error: App {app.name} is currently paused on OpenMemory. Cannot create new memories.", None

                # Use shared vector store context
                mem0_user_id = user_id
                
                # Call mem0 with degradation support
                response = memory_singleton.add_memory_with_degradation(
                    text,
                    metadata={
                        "source_app": "openmemory",
                        "mcp_client": client_name
                    },
                    user_id=mem0_user_id
                )

                # Validate response
                from app.memory_utils import validate_mem0_response
                response_valid, response_message = validate_mem0_response(response, "add_memory")
                if not response_valid:
                    return False, response_message, None

                # Process the response and update database FIRST
                result_data = self._process_memory_response(response, user, app, db)

                # Track evolution operations AFTER database commit to ensure memory IDs exist
                self._track_evolution_operations(response, user_id, client_name, text)

                return True, f"Successfully added memory via app '{client_name}'", result_data
                
            finally:
                db.close()
                
        except Exception as e:
            self.logger.error(f"Error adding memory: {e}")
            return False, f"Error adding to memory: {e}", None

    def _add_memory_chunked(self, text: str, user_id: str, client_name: str, request_id: Optional[int] = None) -> Tuple[bool, str, Optional[Dict]]:
        """Add memory using chunking and transactions."""
        self.logger.warning(f"CHUNKING: Text length {len(text)} exceeds limit, will auto-chunk")
        
        # Chunk the text
        chunks = self.chunk_text(text)
        self.logger.info(f"CHUNKING: Created {len(chunks)} chunks from original text")

        # Get memory client for transaction
        memory_client = self.get_memory_client_safe()
        if not memory_client:
            return False, "Error: Memory system is currently unavailable for chunked operation. Please try again later.", None

        # Use transaction for atomic chunked operation
        from app.memory_transaction import MemoryTransaction
        transaction = MemoryTransaction(memory_client, user_id, client_name)

        # Add all chunks to transaction
        for i, chunk in enumerate(chunks):
            chunk_metadata = {
                "source_app": "openmemory",
                "mcp_client": client_name,
                "chunk_part": f"{i+1}/{len(chunks)}",
                "original_length": len(text),
                "chunk_length": len(chunk)
            }

            chunk_content = f"[Part {i+1}/{len(chunks)}] {chunk}"
            success = transaction.add_memory_chunk(chunk_content, chunk_metadata)

            if not success:
                return False, f"Error: Failed to prepare chunk {i+1}/{len(chunks)} for processing", None

        # Commit the transaction
        commit_success, commit_message, results = transaction.commit()

        if commit_success:
            # Track evolution operations for chunked text
            if results:
                for result in results:
                    self._track_evolution_operations(result, user_id, client_name, text)

            return True, f"Successfully chunked and stored {len(results)} memory pieces from {len(text)} character text via app '{client_name}' (Transaction: {transaction.transaction_id})", {"results": results, "transaction_id": transaction.transaction_id}
        else:
            return False, f"Error: Chunked operation failed - {commit_message}", None

    def search_memory(self, query: str, user_id: str, client_name: str, limit: int = 10) -> Tuple[bool, str, Optional[List]]:
        """
        Search through stored memories.
        
        Returns:
            Tuple[bool, str, Optional[List]]: (success, message, search_results)
        """
        if not user_id:
            return False, "Error: user_id not provided", None
        if not client_name:
            return False, "Error: client_name not provided", None
        if not query.strip():
            return False, "Error: empty query provided", None

        memory_client = self.get_memory_client_safe()
        if not memory_client:
            return False, "Error: Memory system is currently unavailable. Please try again later.", None

        try:
            db = SessionLocal()
            try:
                # Get or create user and app for permission checking
                user, app = get_user_and_app(db, user_id=user_id, app_id=client_name)
                
                # Check if app is active
                if not app.is_active:
                    return False, f"Error: App {app.name} is currently paused on OpenMemory.", None

                # Use shared vector store context
                mem0_user_id = user_id
                
                # Search memories with retry
                from app.memory_utils import search_memories_with_retry
                search_results = search_memories_with_retry(memory_client, query, mem0_user_id, limit)

                # Check if search_results is a string (error message) or empty
                if isinstance(search_results, str):
                    return False, f"Error searching memory: {search_results}", None

                if not search_results:
                    return True, "No memories found matching your query.", []

                # Handle different response formats
                memories_list = []
                if isinstance(search_results, list):
                    memories_list = search_results
                elif isinstance(search_results, dict):
                    # Check if it's a dictionary with 'results' key (like get_all format)
                    if 'results' in search_results:
                        memories_list = search_results['results']
                    else:
                        # Treat the dictionary itself as a single result
                        memories_list = [search_results]
                else:
                    return False, f"Error: Unexpected search result format: {type(search_results)}", None

                # Ensure memories_list is a list
                if not isinstance(memories_list, list):
                    return False, f"Error: Expected list but got: {type(memories_list)}", None

                # Filter results based on permissions
                from app.utils.permissions import check_memory_access_permissions
                filtered_results = []

                for memory in memories_list:
                    # Ensure memory is a dictionary
                    if not isinstance(memory, dict):
                        self.logger.warning(f"Skipping non-dict memory result: {type(memory)}")
                        continue

                    memory_id = memory.get('id')
                    if memory_id:
                        try:
                            memory_uuid = uuid.UUID(memory_id)
                            # Fetch the Memory object from database for permissions check
                            from app.models import Memory
                            memory_obj = db.query(Memory).filter(Memory.id == memory_uuid).first()
                            if memory_obj and check_memory_access_permissions(db, memory_obj, app.id):
                                filtered_results.append(memory)
                        except (ValueError, TypeError):
                            continue

                return True, f"Found {len(filtered_results)} matching memories", filtered_results
                
            finally:
                db.close()
                
        except Exception as e:
            self.logger.error(f"Error searching memory: {e}")
            return False, f"Error searching memory: {e}", None

    def list_memories(self, user_id: str, client_name: str) -> Tuple[bool, str, Optional[List]]:
        """
        List all memories for a user.
        
        Returns:
            Tuple[bool, str, Optional[List]]: (success, message, memories_list)
        """
        if not user_id:
            return False, "Error: user_id not provided", None
        if not client_name:
            return False, "Error: client_name not provided", None

        memory_client = self.get_memory_client_safe()
        if not memory_client:
            return False, "Error: Memory system is currently unavailable. Please try again later.", None

        try:
            db = SessionLocal()
            try:
                # Get or create user and app
                user, app = get_user_and_app(db, user_id=user_id, app_id=client_name)
                
                # Check if app is active
                if not app.is_active:
                    return False, f"Error: App {app.name} is currently paused on OpenMemory.", None

                # Use shared vector store context
                mem0_user_id = user_id
                
                # Get all memories with retry
                from app.memory_utils import get_all_memories_with_retry
                all_memories = get_all_memories_with_retry(memory_client, mem0_user_id)

                # Check if all_memories is a string (error message)
                if isinstance(all_memories, str):
                    return False, f"Error listing memories: {all_memories}", None

                if not all_memories or not isinstance(all_memories, dict) or 'results' not in all_memories:
                    return True, "No memories found.", []

                memories = all_memories['results']

                # Ensure memories is a list
                if not isinstance(memories, list):
                    return False, f"Error: Unexpected memories format: {type(memories)}", None

                # Filter results based on permissions
                from app.utils.permissions import check_memory_access_permissions
                filtered_memories = []

                for memory in memories:
                    # Ensure memory is a dictionary
                    if not isinstance(memory, dict):
                        self.logger.warning(f"Skipping non-dict memory in list: {type(memory)}")
                        continue

                    memory_id = memory.get('id')
                    if memory_id:
                        try:
                            memory_uuid = uuid.UUID(memory_id)
                            # Fetch the Memory object from database for permissions check
                            from app.models import Memory
                            memory_obj = db.query(Memory).filter(Memory.id == memory_uuid).first()
                            if memory_obj and check_memory_access_permissions(db, memory_obj, app.id):
                                filtered_memories.append(memory)
                        except (ValueError, TypeError):
                            continue

                return True, f"Found {len(filtered_memories)} memories", filtered_memories
                
            finally:
                db.close()
                
        except Exception as e:
            self.logger.error(f"Error getting memories: {e}")
            return False, f"Error getting memories: {e}", None

    def _process_memory_response(self, response: Dict, user: User, app: App, db: Session) -> Dict:
        """Process memory response and update database."""
        result_data = {"processed_memories": []}
        
        if isinstance(response, dict) and 'results' in response:
            for result in response['results']:
                memory_id = uuid.UUID(result['id'])
                memory = db.query(Memory).filter(Memory.id == memory_id).first()
                
                event_type = result.get('event', 'ADD')
                
                if event_type == 'ADD':
                    if not memory:
                        memory = Memory(
                            id=memory_id,
                            user_id=user.id,
                            app_id=app.id,
                            content=result['memory'],
                            state=MemoryState.active.value
                        )
                        db.add(memory)
                    else:
                        memory.state = MemoryState.active.value
                        memory.content = result['memory']

                    # Create history entry
                    history = MemoryStatusHistory(
                        memory_id=memory_id,
                        changed_by=user.id,
                        old_state=MemoryState.deleted.value if memory else None,
                        new_state=MemoryState.active.value
                    )
                    db.add(history)
                    
                    result_data["processed_memories"].append({
                        "id": str(memory_id),
                        "event": event_type,
                        "content": result['memory']
                    })
            
            db.commit()
        
        return result_data

    def _track_evolution_operations(self, mem0_response, user_id: str, app_id: str, original_text: str):
        """Track evolution operations from mem0 response."""
        try:
            import time
            # Small delay to ensure database transaction is fully committed
            time.sleep(0.1)

            from app.services.evolution_service import evolution_service

            # Enhanced logging for evolution tracking
            self.logger.info(f"🧠 Tracking evolution operations for user {user_id}, app {app_id}")
            self.logger.info(f"Original text length: {len(original_text)} chars")
            self.logger.info(f"mem0 response type: {type(mem0_response)}")

            # Log response structure for debugging
            if isinstance(mem0_response, dict):
                self.logger.info(f"Response keys: {list(mem0_response.keys())}")
                if 'results' in mem0_response:
                    self.logger.info(f"Results count: {len(mem0_response['results'])}")
                    for i, result in enumerate(mem0_response['results']):
                        if isinstance(result, dict) and 'event' in result:
                            self.logger.info(f"Result {i}: event={result['event']}, memory={result.get('memory', 'N/A')[:100]}...")

            # Extract evolution statistics
            operations = evolution_service.extract_evolution_stats(
                mem0_response, user_id, app_id, original_text
            )

            # Enhanced operation logging and storage
            self.logger.info(f"📊 Extracted {len(operations)} evolution operations")

            stored_count = 0
            for i, operation_data in enumerate(operations):
                op_type = operation_data.get('operation_type', 'UNKNOWN')
                reasoning = operation_data.get('reasoning', 'No reasoning')
                confidence = operation_data.get('confidence_score', 0.0)

                self.logger.info(f"Operation {i+1}: {op_type} (confidence: {confidence:.2f}) - {reasoning}")

                operation_id = evolution_service.store_evolution_operation(operation_data)
                if operation_id:
                    stored_count += 1
                    self.logger.debug(f"✅ Stored evolution operation {operation_id}")
                else:
                    self.logger.warning(f"❌ Failed to store evolution operation {i+1}")

            self.logger.info(f"📈 Evolution tracking complete: {stored_count}/{len(operations)} operations stored")

        except Exception as e:
            # Don't fail the main operation if evolution tracking fails
            self.logger.error(f"💥 Error tracking evolution operations: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Continue without failing the memory operation

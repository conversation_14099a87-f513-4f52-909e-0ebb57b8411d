"""
Memory Transaction Handler for OpenMemory

This module provides transaction-like behavior for chunked memory operations.
Ensures atomic behavior and provides rollback capabilities for memory operations.

Separated from MCP server to provide clean transaction handling.
"""

import logging
import uuid
import datetime
from typing import List, Tuple, Any, Dict
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.models import Memory, MemoryState, MemoryStatusHistory
from app.memory_utils import add_memory_with_retry, validate_mem0_response, search_memories_with_retry


class MemoryTransaction:
    """
    Transaction-like behavior for chunked memory operations.
    Ensures atomic behavior and provides rollback capabilities.
    """

    def __init__(self, memory_client, user_id: str, client_name: str):
        self.memory_client = memory_client
        self.user_id = user_id
        self.client_name = client_name
        self.operations = []
        self.results = []
        self.committed = False
        self.transaction_id = str(uuid.uuid4())
        self.start_time = datetime.datetime.now()

        logging.info(f"Created memory transaction {self.transaction_id} for user {user_id}")

    def add_memory_chunk(self, content: str, metadata: dict = None) -> bool:
        """Add a memory chunk to the transaction."""
        if self.committed:
            raise ValueError("Cannot add operations to committed transaction")

        # Add transaction metadata
        chunk_metadata = metadata.copy() if metadata else {}
        chunk_metadata.update({
            'transaction_id': self.transaction_id,
            'chunk_index': len(self.operations),
            'source_app': 'openmemory',
            'mcp_client': self.client_name,
            'is_chunk': True
        })

        # Store operation for later execution
        operation = {
            'type': 'add_memory',
            'content': content,
            'metadata': chunk_metadata,
            'chunk_index': len(self.operations)
        }

        self.operations.append(operation)
        logging.debug(f"Added chunk {len(self.operations)-1} to transaction {self.transaction_id}")
        return True

    def commit(self) -> Tuple[bool, str, List[Any]]:
        """Execute all operations in the transaction."""
        if self.committed:
            raise ValueError("Transaction already committed")

        if not self.operations:
            return True, "No operations to commit", []

        logging.info(f"Committing transaction {self.transaction_id} with {len(self.operations)} operations")

        try:
            # Execute all operations
            success = True
            for i, operation in enumerate(self.operations):
                try:
                    logging.debug(f"Executing operation {i+1}/{len(self.operations)} in transaction {self.transaction_id}")

                    if operation['type'] == 'add_memory':
                        result = add_memory_with_retry(
                            self.memory_client,
                            operation['content'],
                            user_id=self.user_id,
                            metadata=operation['metadata']
                        )

                        # Validate the result
                        success_status, message = validate_mem0_response(result, "add_memory")
                        if not success_status:
                            logging.error(f"Transaction {self.transaction_id} operation {i} failed validation: {message}")
                            success = False
                            break

                        self.results.append(result)
                        logging.debug(f"Transaction {self.transaction_id} operation {i} completed successfully")

                except Exception as e:
                    logging.error(f"Transaction {self.transaction_id} operation {i} failed: {str(e)}")
                    success = False
                    break

            if not success:
                logging.warning(f"Transaction {self.transaction_id} failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Transaction failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            # Verify all chunks are accessible
            if not self._verify_chunks():
                logging.error(f"Transaction {self.transaction_id} chunk verification failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Chunk verification failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            self.committed = True
            duration = (datetime.datetime.now() - self.start_time).total_seconds()
            logging.info(f"Transaction {self.transaction_id} committed successfully in {duration:.3f}s")

            return True, f"Transaction committed successfully with {len(self.results)} operations", self.results

        except Exception as e:
            logging.error(f"Transaction {self.transaction_id} commit failed with exception: {str(e)}")
            rollback_success, rollback_message = self.rollback()
            return False, f"Transaction failed with exception and rollback {'succeeded' if rollback_success else 'failed'}: {str(e)}", []

    def rollback(self) -> Tuple[bool, str]:
        """Roll back the transaction by removing any stored chunks."""
        if not self.results:
            return True, "No operations to rollback"

        logging.warning(f"Rolling back transaction {self.transaction_id} with {len(self.results)} operations")

        rollback_errors = []
        successful_rollbacks = 0

        for i, result in enumerate(self.results):
            try:
                if isinstance(result, dict) and 'results' in result:
                    for memory_result in result['results']:
                        if 'id' in memory_result:
                            memory_id = memory_result['id']
                            # Mark memory as deleted in database
                            try:
                                db = SessionLocal()
                                try:
                                    memory = db.query(Memory).filter(Memory.id == uuid.UUID(memory_id)).first()
                                    if memory:
                                        memory.state = MemoryState.deleted.value
                                        memory.deleted_at = datetime.datetime.now(datetime.UTC)

                                        # Create history entry
                                        history = MemoryStatusHistory(
                                            memory_id=uuid.UUID(memory_id),
                                            changed_by=None,  # System rollback
                                            old_state=MemoryState.active.value,
                                            new_state=MemoryState.deleted.value
                                        )
                                        db.add(history)
                                        db.commit()
                                        successful_rollbacks += 1
                                        logging.debug(f"Rolled back memory {memory_id} in transaction {self.transaction_id}")
                                finally:
                                    db.close()
                            except Exception as db_error:
                                rollback_errors.append(f"Database rollback failed for {memory_id}: {str(db_error)}")

            except Exception as e:
                rollback_errors.append(f"Rollback operation {i} failed: {str(e)}")

        self.results = []

        if rollback_errors:
            error_message = f"Rollback partially failed: {len(rollback_errors)} errors, {successful_rollbacks} successful"
            logging.error(f"Transaction {self.transaction_id} rollback errors: {rollback_errors}")
            return False, error_message
        else:
            success_message = f"Rollback successful: {successful_rollbacks} operations rolled back"
            logging.info(f"Transaction {self.transaction_id} rollback completed: {success_message}")
            return True, success_message

    def _verify_chunks(self) -> bool:
        """Verify all chunks are accessible after commit."""
        if not self.results:
            return True

        logging.debug(f"Verifying {len(self.results)} chunks in transaction {self.transaction_id}")

        for i, result in enumerate(self.results):
            try:
                if isinstance(result, dict) and 'results' in result:
                    for memory_result in result['results']:
                        if 'id' in memory_result:
                            memory_id = memory_result['id']

                            # Verify memory exists in database
                            db = SessionLocal()
                            try:
                                memory = db.query(Memory).filter(
                                    Memory.id == uuid.UUID(memory_id),
                                    Memory.state == MemoryState.active.value
                                ).first()

                                if not memory:
                                    logging.error(f"Chunk verification failed: memory {memory_id} not found in database")
                                    return False

                            finally:
                                db.close()

                            # Verify memory is accessible via vector store
                            try:
                                search_result = search_memories_with_retry(
                                    self.memory_client,
                                    memory_result.get('memory', '')[:50],  # Search with first 50 chars
                                    user_id=self.user_id,
                                    limit=1
                                )

                                # Check if our memory is in the search results
                                found = False
                                if search_result:
                                    for search_memory in search_result:
                                        if search_memory.get('id') == memory_id:
                                            found = True
                                            break

                                if not found:
                                    logging.warning(f"Chunk verification: memory {memory_id} not immediately searchable (may be indexing)")
                                    # Don't fail verification for search issues as indexing may be delayed

                            except Exception as search_error:
                                logging.warning(f"Chunk verification search failed for {memory_id}: {str(search_error)}")
                                # Don't fail verification for search issues

            except Exception as e:
                logging.error(f"Chunk verification failed for result {i}: {str(e)}")
                return False

        logging.debug(f"All chunks verified successfully for transaction {self.transaction_id}")
        return True

    def get_status(self) -> Dict:
        """Get transaction status information."""
        return {
            'transaction_id': self.transaction_id,
            'user_id': self.user_id,
            'client_name': self.client_name,
            'operations_count': len(self.operations),
            'results_count': len(self.results),
            'committed': self.committed,
            'start_time': self.start_time.isoformat(),
            'duration_seconds': (datetime.datetime.now() - self.start_time).total_seconds()
        }

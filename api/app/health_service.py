"""
Health Service Layer for OpenMemory

This module provides system health monitoring, degradation status, and operation metrics
collection. It consolidates health-related functionality from degradation_status.py and
enhanced_logging.py into a clean service interface.

Separated from MCP server to provide focused health monitoring capabilities.
"""

import json
import logging
import datetime
from typing import Dict, Any, Tuple

from app.degradation_status import get_system_health_status, format_status_for_display
from app.enhanced_logging import operation_logger


class HealthService:
    """Service for system health monitoring and metrics collection."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_system_health(self) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Get comprehensive system health status including degradation information.
        
        Returns:
            Tuple[bool, str, Dict]: (success, formatted_message, raw_data)
        """
        try:
            # Get system health status from degradation_status module
            health_status = get_system_health_status()
            
            # Format for display
            formatted_status = format_status_for_display(health_status)
            
            # Create comprehensive response
            formatted_message = f"{formatted_status}\n\n--- Raw JSON Status ---\n{json.dumps(health_status, indent=2)}"
            
            return True, formatted_message, health_status
            
        except Exception as e:
            self.logger.exception(f"Error getting system health status: {e}")
            error_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "overall_health": "error",
                "error": str(e),
                "recommendations": ["Check system logs for detailed error information"]
            }
            return False, f"Error getting system health status: {e}", error_data
    
    def get_operation_metrics(self) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Get comprehensive operation metrics including timing, success rates, and error analysis.
        
        Returns:
            Tuple[bool, str, Dict]: (success, formatted_message, raw_data)
        """
        try:
            # Get operation metrics from the enhanced logger
            metrics = operation_logger.get_operation_metrics()
            active_operations = operation_logger.get_active_operations()
            
            # Format metrics for display
            formatted_output = self._format_operation_metrics(metrics, active_operations)
            
            # Prepare raw data
            raw_data = {
                "metrics": metrics,
                "active_operations": active_operations
            }
            
            return True, formatted_output, raw_data
            
        except Exception as e:
            self.logger.exception(f"Error getting operation metrics: {e}")
            error_data = {
                "error": str(e),
                "timestamp": datetime.datetime.now().isoformat()
            }
            return False, f"Error getting operation metrics: {e}", error_data
    
    def _format_operation_metrics(self, metrics: Dict[str, Any], active_operations: Dict[str, Any]) -> str:
        """Format operation metrics for human-readable display."""
        formatted_output = ["=== Memory Operation Metrics ==="]
        formatted_output.append(f"Metrics collected at: {datetime.datetime.fromtimestamp(metrics['metrics_timestamp']).isoformat()}")
        formatted_output.append(f"Active operations: {metrics['active_operations_count']}")
        
        # Show active operations if any
        if active_operations:
            formatted_output.append("\n--- Currently Active Operations ---")
            for op_id, op_info in active_operations.items():
                formatted_output.append(f"  {op_id}: {op_info.get('operation_type', 'unknown')} ({op_info.get('duration_ms', 0)}ms)")
        
        if metrics['operations']:
            formatted_output.append("\n--- Operation Statistics ---")
            for op_type, stats in metrics['operations'].items():
                success_rate = (stats['success_count'] / stats['total_count'] * 100) if stats['total_count'] > 0 else 0
                formatted_output.append(f"\n{op_type.upper()}:")
                formatted_output.append(f"  Total operations: {stats['total_count']}")
                formatted_output.append(f"  Success rate: {success_rate:.1f}% ({stats['success_count']}/{stats['total_count']})")
                formatted_output.append(f"  Failures: {stats['failure_count']}")
                formatted_output.append(f"  Total retries: {stats['retry_count']}")
                
                # Performance metrics
                if stats.get('avg_duration_ms') is not None:
                    formatted_output.append(f"  Average duration: {stats['avg_duration_ms']:.1f}ms")
                    if stats.get('min_duration_ms') is not None:
                        formatted_output.append(f"  Duration range: {stats['min_duration_ms']}-{stats['max_duration_ms']}ms")
                
                # Performance distribution
                if stats.get('performance_distribution'):
                    perf_dist = stats['performance_distribution']
                    total_ops = sum(perf_dist.values())
                    if total_ops > 0:
                        formatted_output.append(f"  Performance distribution:")
                        for category, count in perf_dist.items():
                            percentage = (count / total_ops * 100) if total_ops > 0 else 0
                            formatted_output.append(f"    {category}: {count} ({percentage:.1f}%)")
                
                # Error breakdown
                if stats['error_types']:
                    formatted_output.append(f"  Error breakdown:")
                    for error_type, count in stats['error_types'].items():
                        error_percentage = (count / stats['failure_count'] * 100) if stats['failure_count'] > 0 else 0
                        formatted_output.append(f"    {error_type}: {count} ({error_percentage:.1f}% of failures)")
                
                if stats['last_operation']:
                    last_op_time = datetime.datetime.fromtimestamp(stats['last_operation'])
                    formatted_output.append(f"  Last operation: {last_op_time.isoformat()}")
        else:
            formatted_output.append("\nNo operation statistics available yet.")
        
        # Add raw JSON for programmatic access
        formatted_output.append("\n\n--- Raw JSON Metrics ---")
        formatted_output.append(json.dumps({
            "metrics": metrics,
            "active_operations": active_operations
        }, indent=2))
        
        return "\n".join(formatted_output)
    
    def get_health_summary(self) -> Dict[str, Any]:
        """
        Get a concise health summary for quick status checks.
        
        Returns:
            Dict containing key health indicators
        """
        try:
            # Get basic health status
            success, _, health_data = self.get_system_health()
            
            if not success:
                return {
                    "status": "error",
                    "message": "Failed to retrieve health status",
                    "timestamp": datetime.datetime.now().isoformat()
                }
            
            # Get operation metrics
            metrics_success, _, metrics_data = self.get_operation_metrics()
            
            # Extract key indicators
            summary = {
                "status": health_data.get("overall_health", "unknown"),
                "degraded_mode": health_data.get("degradation", {}).get("degraded_mode", False),
                "client_healthy": health_data.get("client_healthy", False),
                "timestamp": health_data.get("timestamp"),
                "active_operations": 0,
                "total_operations": 0,
                "overall_success_rate": 0.0
            }
            
            # Add metrics summary if available
            if metrics_success and metrics_data.get("metrics"):
                metrics = metrics_data["metrics"]
                summary["active_operations"] = metrics.get("active_operations_count", 0)
                
                # Calculate overall statistics
                total_ops = 0
                total_success = 0
                
                for op_stats in metrics.get("operations", {}).values():
                    total_ops += op_stats.get("total_count", 0)
                    total_success += op_stats.get("success_count", 0)
                
                summary["total_operations"] = total_ops
                summary["overall_success_rate"] = (total_success / total_ops * 100) if total_ops > 0 else 0.0
            
            return summary
            
        except Exception as e:
            self.logger.exception(f"Error getting health summary: {e}")
            return {
                "status": "error",
                "message": f"Error getting health summary: {e}",
                "timestamp": datetime.datetime.now().isoformat()
            }
    
    def is_system_healthy(self) -> bool:
        """
        Quick health check - returns True if system is operating normally.
        
        Returns:
            bool: True if system is healthy, False otherwise
        """
        try:
            success, _, health_data = self.get_system_health()
            if not success:
                return False
            
            overall_health = health_data.get("overall_health", "unknown")
            return overall_health == "healthy"
            
        except Exception as e:
            self.logger.exception(f"Error checking system health: {e}")
            return False
    
    def get_degradation_status(self) -> Dict[str, Any]:
        """
        Get detailed degradation status information.
        
        Returns:
            Dict containing degradation status details
        """
        try:
            success, _, health_data = self.get_system_health()
            if not success:
                return {"error": "Failed to retrieve degradation status"}
            
            return health_data.get("degradation", {})
            
        except Exception as e:
            self.logger.exception(f"Error getting degradation status: {e}")
            return {"error": str(e)}


# Global health service instance
health_service = HealthService()

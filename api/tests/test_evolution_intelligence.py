"""Test suite for evolution intelligence core functionality.

This module tests the core evolution logic, database operations,
and MCP tool validation for the evolution intelligence enhancement.
"""

import pytest
import uuid
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import tempfile
import os

from app.database import Base
from app.models import User, App, Memory, EvolutionOperation, EvolutionInsight, EvolutionOperationType
from sqlalchemy import MetaData


class TestEvolutionIntelligenceCore:
    """Test core evolution intelligence functionality."""
    
    @pytest.fixture(autouse=True)
    def setup_test_data(self, test_db):
        """Set up test data using the existing database session."""
        # Store session for use in tests
        self.session = test_db
        
        # Create test user and app
        self.test_user = User(
            id=uuid.uuid4(),
            user_id="test_user_123",
            email="<EMAIL>",
            name="Test User"
        )
        self.session.add(self.test_user)
        
        self.test_app = App(
            id=uuid.uuid4(),
            name="Test App",
            owner_id=self.test_user.id
        )
        self.session.add(self.test_app)
        
        self.test_memory = Memory(
            id=uuid.uuid4(),
            content="Test memory content",
            user_id=self.test_user.id,
            app_id=self.test_app.id
        )
        self.session.add(self.test_memory)
        
        self.session.commit()
        
        yield
        
        # Cleanup is handled by the test_db fixture
    
    def test_evolution_operation_creation(self):
        """Test creating evolution operations."""
        operation = EvolutionOperation(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            memory_id=self.test_memory.id,
            operation_type=EvolutionOperationType.ADD,
            candidate_fact="New fact to add",
            existing_memory_content="Existing content",
            similarity_score=0.85,
            confidence_score=0.92,
            reasoning="High confidence addition based on similarity analysis",
            metadata={"source": "test", "priority": "high"}
        )
        
        self.session.add(operation)
        self.session.commit()
        
        # Verify operation was created
        saved_operation = self.session.query(EvolutionOperation).filter_by(
            user_id=self.test_user.id
        ).first()
        
        assert saved_operation is not None
        assert saved_operation.operation_type == EvolutionOperationType.ADD
        assert saved_operation.candidate_fact == "New fact to add"
        assert saved_operation.similarity_score == 0.85
        assert saved_operation.confidence_score == 0.92
        assert saved_operation.metadata["source"] == "test"
    
    def test_evolution_operation_types(self):
        """Test all evolution operation types."""
        operation_types = [
            EvolutionOperationType.ADD,
            EvolutionOperationType.UPDATE,
            EvolutionOperationType.DELETE,
            EvolutionOperationType.NOOP
        ]
        
        operations = []
        for i, op_type in enumerate(operation_types):
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                operation_type=op_type,
                candidate_fact=f"Test fact {i}",
                confidence_score=0.8 + (i * 0.05),
                reasoning=f"Test reasoning for {op_type.value}"
            )
            operations.append(operation)
            self.session.add(operation)
        
        self.session.commit()
        
        # Verify all operations were created with correct types
        saved_operations = self.session.query(EvolutionOperation).filter_by(
            user_id=self.test_user.id
        ).all()
        
        assert len(saved_operations) == 4
        saved_types = [op.operation_type for op in saved_operations]
        assert set(saved_types) == set(operation_types)
    
    def test_evolution_insight_creation(self):
        """Test creating evolution insights."""
        insight_date = datetime.now().date()
        
        insight = EvolutionInsight(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            date=insight_date,
            total_operations=100,
            add_operations=40,
            update_operations=30,
            delete_operations=10,
            noop_operations=20,
            learning_efficiency=0.75,
            conflict_resolution_count=5,
            average_confidence=0.88,
            average_similarity=0.82
        )
        
        self.session.add(insight)
        self.session.commit()
        
        # Verify insight was created
        saved_insight = self.session.query(EvolutionInsight).filter_by(
            user_id=self.test_user.id,
            date=insight_date
        ).first()
        
        assert saved_insight is not None
        assert saved_insight.total_operations == 100
        assert saved_insight.learning_efficiency == 0.75
        assert saved_insight.average_confidence == 0.88
        assert saved_insight.conflict_resolution_count == 5
    
    def test_evolution_insight_unique_constraint(self):
        """Test unique constraint on user_id, app_id, date."""
        insight_date = datetime.now().date()
        
        # Create first insight
        insight1 = EvolutionInsight(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            date=insight_date,
            total_operations=50,
            add_operations=20,
            update_operations=15,
            delete_operations=5,
            noop_operations=10,
            learning_efficiency=0.70,
            conflict_resolution_count=2,
            average_confidence=0.85,
            average_similarity=0.80
        )
        
        self.session.add(insight1)
        self.session.commit()
        
        # Try to create duplicate insight (should fail)
        insight2 = EvolutionInsight(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            date=insight_date,
            total_operations=75,
            add_operations=30,
            update_operations=25,
            delete_operations=10,
            noop_operations=10,
            learning_efficiency=0.80,
            conflict_resolution_count=3,
            average_confidence=0.90,
            average_similarity=0.85
        )
        
        self.session.add(insight2)
        
        # Should raise integrity error due to unique constraint
        with pytest.raises(Exception):  # SQLAlchemy will raise IntegrityError
            self.session.commit()
    
    def test_foreign_key_constraints(self):
        """Test foreign key constraints for evolution tables."""
        # Test evolution operation with invalid user_id
        invalid_user_id = uuid.uuid4()
        
        operation = EvolutionOperation(
            id=uuid.uuid4(),
            user_id=invalid_user_id,
            app_id=self.test_app.id,
            operation_type=EvolutionOperationType.ADD,
            candidate_fact="Test fact",
            confidence_score=0.8,
            reasoning="Test reasoning"
        )
        
        self.session.add(operation)
        
        # Should raise foreign key constraint error
        with pytest.raises(Exception):
            self.session.commit()
    
    def test_cascade_delete_behavior(self):
        """Test cascade delete behavior for evolution tables."""
        # Create evolution operation
        operation = EvolutionOperation(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            memory_id=self.test_memory.id,
            operation_type=EvolutionOperationType.UPDATE,
            candidate_fact="Updated fact",
            confidence_score=0.9,
            reasoning="High confidence update"
        )
        
        self.session.add(operation)
        self.session.commit()
        
        operation_id = operation.id
        
        # Delete the user (should cascade to evolution operations)
        self.session.delete(self.test_user)
        self.session.commit()
        
        # Verify operation was deleted
        deleted_operation = self.session.query(EvolutionOperation).filter_by(
            id=operation_id
        ).first()
        
        assert deleted_operation is None
    
    def test_memory_set_null_behavior(self):
        """Test SET NULL behavior when memory is deleted."""
        # Create evolution operation linked to memory
        operation = EvolutionOperation(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            memory_id=self.test_memory.id,
            operation_type=EvolutionOperationType.DELETE,
            candidate_fact="Memory to delete",
            confidence_score=0.95,
            reasoning="High confidence deletion"
        )
        
        self.session.add(operation)
        self.session.commit()
        
        operation_id = operation.id
        
        # Delete the memory (should set memory_id to NULL)
        self.session.delete(self.test_memory)
        self.session.commit()
        
        # Verify operation still exists but memory_id is NULL
        updated_operation = self.session.query(EvolutionOperation).filter_by(
            id=operation_id
        ).first()
        
        assert updated_operation is not None
        assert updated_operation.memory_id is None
    
    def test_evolution_operation_indexes(self):
        """Test that indexes are working for evolution operations."""
        # Create multiple operations with different timestamps
        base_time = datetime.now()
        operations = []
        
        for i in range(10):
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact=f"Fact {i}",
                confidence_score=0.8,
                reasoning=f"Reasoning {i}",
                created_at=base_time + timedelta(minutes=i)
            )
            operations.append(operation)
            self.session.add(operation)
        
        self.session.commit()
        
        # Test user_id + created_at index (should be fast)
        result = self.session.query(EvolutionOperation).filter(
            EvolutionOperation.user_id == self.test_user.id,
            EvolutionOperation.created_at >= base_time + timedelta(minutes=5)
        ).all()
        
        assert len(result) == 5
        
        # Test app_id + operation_type index
        result = self.session.query(EvolutionOperation).filter(
            EvolutionOperation.app_id == self.test_app.id,
            EvolutionOperation.operation_type == EvolutionOperationType.ADD
        ).all()
        
        assert len(result) == 10
    
    def test_evolution_insight_indexes(self):
        """Test that indexes are working for evolution insights."""
        # Create insights for different dates
        base_date = datetime.now().date()
        insights = []
        
        for i in range(5):
            insight = EvolutionInsight(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                date=base_date + timedelta(days=i),
                total_operations=10 * (i + 1),
                add_operations=5 * (i + 1),
                update_operations=3 * (i + 1),
                delete_operations=1 * (i + 1),
                noop_operations=1 * (i + 1),
                learning_efficiency=0.7 + (i * 0.05),
                conflict_resolution_count=i,
                average_confidence=0.8 + (i * 0.02),
                average_similarity=0.75 + (i * 0.03)
            )
            insights.append(insight)
            self.session.add(insight)
        
        self.session.commit()
        
        # Test user_id + date index
        result = self.session.query(EvolutionInsight).filter(
            EvolutionInsight.user_id == self.test_user.id,
            EvolutionInsight.date >= base_date + timedelta(days=2)
        ).all()
        
        assert len(result) == 3
        
        # Test app_id + date index
        result = self.session.query(EvolutionInsight).filter(
            EvolutionInsight.app_id == self.test_app.id,
            EvolutionInsight.date == base_date + timedelta(days=1)
        ).first()
        
        assert result is not None
        assert result.total_operations == 20
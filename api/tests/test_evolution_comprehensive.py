"""
Comprehensive Evolution Intelligence Tests

This module contains comprehensive tests for the evolution intelligence features
including custom prompts, evolution tracking, and MCP tools.
"""

import pytest
import uuid
import json
from datetime import datetime, timezone, date, timedelta
from unittest.mock import Mock, patch, MagicMock

from app.services.evolution_service import EvolutionService, evolution_service
from app.models import EvolutionOperation, EvolutionInsight
from app.memory_service import MemoryService
from app.utils.evolution_prompts import get_default_technical_prompts


class TestEvolutionPrompts:
    """Test evolution prompt functionality."""
    
    def test_default_technical_prompts_exist(self):
        """Test that default technical prompts are available."""
        prompts = get_default_technical_prompts()
        
        assert "custom_fact_extraction_prompt" in prompts
        assert "custom_update_memory_prompt" in prompts
        assert len(prompts["custom_fact_extraction_prompt"]) > 1000
        assert len(prompts["custom_update_memory_prompt"]) > 1000
        assert "technical" in prompts["custom_fact_extraction_prompt"].lower()
        assert "programming" in prompts["custom_fact_extraction_prompt"].lower()
    
    def test_fact_extraction_prompt_structure(self):
        """Test that fact extraction prompt has proper structure."""
        prompts = get_default_technical_prompts()
        fact_prompt = prompts["custom_fact_extraction_prompt"]
        
        # Check for key sections
        assert "FOCUS ON THESE TECHNICAL AREAS" in fact_prompt
        assert "IGNORE THESE NON-TECHNICAL AREAS" in fact_prompt
        assert "OUTPUT FORMAT" in fact_prompt
        assert "EXAMPLES" in fact_prompt
        assert "JSON" in fact_prompt
    
    def test_update_memory_prompt_structure(self):
        """Test that update memory prompt has proper structure."""
        prompts = get_default_technical_prompts()
        update_prompt = prompts["custom_update_memory_prompt"]
        
        # Check for key sections
        assert "EVOLUTION OPERATIONS" in update_prompt
        assert "ADD" in update_prompt and "UPDATE" in update_prompt
        assert "DELETE" in update_prompt and "NOOP" in update_prompt
        assert "TECHNICAL EVOLUTION RULES" in update_prompt
        assert "OUTPUT FORMAT" in update_prompt


class TestEvolutionService:
    """Test evolution service functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.service = EvolutionService()
        self.user_id = str(uuid.uuid4())
        self.app_id = str(uuid.uuid4())
    
    def test_extract_evolution_stats_fallback(self):
        """Test evolution stats extraction with fallback behavior."""
        # Mock mem0 response with no specific evolution data
        mock_response = Mock()
        mock_response.results = None
        mock_response.memories = None
        
        operations = self.service.extract_evolution_stats(
            mock_response, self.user_id, self.app_id, "test text"
        )
        
        assert len(operations) == 1
        assert operations[0]["operation_type"] == "ADD"
        assert operations[0]["user_id"] == self.user_id
        assert operations[0]["app_id"] == self.app_id
        assert "fallback_operation" in operations[0]["metadata"]
    
    def test_extract_evolution_stats_with_results(self):
        """Test evolution stats extraction with mem0 results."""
        # Test the _parse_mem0_result method directly for better control
        mock_result = Mock()
        mock_result.operation = "UPDATE"
        mock_result.confidence = 0.9
        mock_result.reasoning = "Enhanced existing knowledge"
        mock_result.memory_id = str(uuid.uuid4())

        # Test the parsing method directly
        operation_data = self.service._parse_mem0_result(
            mock_result, self.user_id, self.app_id, "test text"
        )

        # The method should return an operation data dict
        assert operation_data is not None
        assert operation_data["user_id"] == self.user_id
        assert operation_data["app_id"] == self.app_id
        assert "operation_type" in operation_data
        assert "confidence_score" in operation_data
    
    @patch('app.services.evolution_service.SessionLocal')
    def test_store_evolution_operation(self, mock_session_local):
        """Test storing evolution operation in database."""
        # Mock database session
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        operation_data = {
            "user_id": self.user_id,
            "app_id": self.app_id,
            "operation_type": "ADD",
            "candidate_fact": "Test fact",
            "confidence_score": 0.8,
            "reasoning": "Test reasoning"
        }
        
        # Mock the evolution operation creation
        mock_evolution_op = Mock()
        mock_evolution_op.id = uuid.uuid4()
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        mock_db.close.return_value = None
        
        with patch.object(self.service, 'update_daily_insights', return_value=True):
            result = self.service.store_evolution_operation(operation_data)
        
        # Verify database operations were called
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_calculate_daily_metrics(self):
        """Test daily metrics calculation."""
        # Mock database session and operations
        mock_db = Mock()
        
        # Create mock operations
        mock_ops = []
        for op_type in ["ADD", "UPDATE", "DELETE", "NOOP"]:
            mock_op = Mock()
            mock_op.operation_type = op_type
            mock_op.confidence_score = 0.8
            mock_op.similarity_score = 0.7
            mock_ops.append(mock_op)
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_ops
        
        metrics = self.service._calculate_daily_metrics(
            mock_db, self.user_id, self.app_id, date.today()
        )
        
        assert metrics["total_operations"] == 4
        assert metrics["add_operations"] == 1
        assert metrics["update_operations"] == 1
        assert metrics["delete_operations"] == 1
        assert metrics["noop_operations"] == 1
        assert metrics["learning_efficiency"] == 0.5  # (1 UPDATE + 1 DELETE) / 4 total
        assert metrics["average_confidence"] == 0.8
        assert metrics["average_similarity"] == 0.7
    
    def test_get_evolution_metrics_formatting(self):
        """Test evolution metrics formatting."""
        with patch.object(self.service, '_calculate_daily_metrics') as mock_calc:
            with patch('app.services.evolution_service.SessionLocal') as mock_session:
                mock_db = Mock()
                mock_session.return_value = mock_db
                
                # Mock insights data
                mock_insight = Mock()
                mock_insight.total_operations = 10
                mock_insight.add_operations = 5
                mock_insight.update_operations = 3
                mock_insight.delete_operations = 2
                mock_insight.noop_operations = 0
                mock_insight.average_confidence = 0.85
                
                mock_db.query.return_value.filter.return_value.all.return_value = [mock_insight]
                
                result = self.service.get_evolution_metrics(self.user_id, "week")
                
                assert "Evolution Intelligence Metrics" in result
                assert "Learning Efficiency: 50.0%" in result  # (3+2)/10 * 100
                assert "5 ADD, 3 UPDATE, 2 DELETE, 0 NOOP" in result
                assert "85.0% average confidence" in result
    
    def test_get_learning_insights_no_data(self):
        """Test learning insights with no data."""
        with patch('app.services.evolution_service.SessionLocal') as mock_session:
            mock_db = Mock()
            mock_session.return_value = mock_db
            mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = []
            
            result = self.service.get_learning_insights(self.user_id)
            
            assert "No evolution data available yet" in result
    
    def test_get_evolution_monitor_formatting(self):
        """Test evolution monitor formatting."""
        with patch('app.services.evolution_service.SessionLocal') as mock_session:
            mock_db = Mock()
            mock_session.return_value = mock_db
            
            # Mock recent operations
            mock_op = Mock()
            mock_op.operation_type = "UPDATE"
            mock_op.created_at = datetime.now(timezone.utc) - timedelta(minutes=5)
            mock_op.confidence_score = 0.9
            
            mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = [mock_op]
            mock_db.query.return_value.filter.return_value.count.return_value = 5  # Today's operations
            
            result = self.service.get_evolution_monitor(self.user_id, limit=5)
            
            assert "Recent Evolution Activity" in result
            assert "UPDATE - Enhanced existing knowledge" in result
            assert "System Intelligence Status" in result
            assert "5 intelligent operations today" in result


class TestMemoryServiceIntegration:
    """Test memory service integration with evolution tracking."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.memory_service = MemoryService()
        self.user_id = str(uuid.uuid4())
        self.client_name = "test_client"
    
    @patch('app.memory_service.MemoryService.get_memory_singleton_safe')
    @patch('app.memory_service.get_user_and_app')
    @patch('app.memory_service.SessionLocal')
    def test_evolution_tracking_in_add_memory(self, mock_session, mock_get_user_app, mock_singleton):
        """Test that evolution tracking is called during memory addition."""
        # Mock dependencies
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        mock_user = Mock()
        mock_app = Mock()
        mock_app.is_active = True
        mock_get_user_app.return_value = (mock_user, mock_app)
        
        mock_memory_client = Mock()
        mock_response = Mock()
        mock_singleton.return_value = mock_memory_client
        mock_memory_client.add_memory_with_degradation.return_value = mock_response
        
        # Mock validation
        with patch('app.memory_utils.validate_mem0_response', return_value=(True, "Success")):
            with patch.object(self.memory_service, '_track_evolution_operations') as mock_track:
                with patch.object(self.memory_service, '_process_memory_response', return_value={}):
                    success, message, result = self.memory_service._add_memory_single(
                        "test text", self.user_id, self.client_name
                    )
                    
                    # Verify evolution tracking was called
                    mock_track.assert_called_once_with(mock_response, self.user_id, self.client_name, "test text")
    
    def test_track_evolution_operations_error_handling(self):
        """Test that evolution tracking errors don't break memory operations."""
        mock_response = Mock()
        
        with patch('app.services.evolution_service.evolution_service.extract_evolution_stats', side_effect=Exception("Test error")):
            # This should not raise an exception
            self.memory_service._track_evolution_operations(mock_response, self.user_id, self.client_name, "test text")


class TestMCPToolsIntegration:
    """Test MCP tools integration with evolution service."""
    
    def test_mcp_tools_import_evolution_service(self):
        """Test that MCP tools can import evolution service."""
        # This test ensures the import structure works
        from app.services.evolution_service import evolution_service
        
        assert hasattr(evolution_service, 'get_evolution_metrics')
        assert hasattr(evolution_service, 'get_learning_insights')
        assert hasattr(evolution_service, 'get_evolution_monitor')
    
    @patch('app.services.evolution_service.evolution_service.get_evolution_metrics')
    def test_evolution_metrics_mcp_tool_call(self, mock_get_metrics):
        """Test evolution metrics MCP tool functionality."""
        mock_get_metrics.return_value = "Test metrics response"
        
        # Import and test the MCP tool function
        from app.mcp_server import get_evolution_metrics
        
        # This would normally be called by the MCP framework
        # We're testing the function exists and can be called
        assert callable(get_evolution_metrics)


if __name__ == "__main__":
    pytest.main([__file__])

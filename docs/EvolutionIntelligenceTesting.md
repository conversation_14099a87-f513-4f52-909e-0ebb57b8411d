# Evolution Intelligence Testing Guide

This guide provides comprehensive instructions for testing both **Enable Evolution** and **Auto-Optimization** features in Memory Master v2.

## Overview

The Evolution Intelligence system has two main features:

1. **Enable Evolution**: Controls whether memory evolution processing is active
2. **Auto-Optimization**: Automatically optimizes memory structure and performance when enabled

## Prerequisites

- Memory Master v2 containers running (memory-mcp, memory-ui, memory-qdrant)
- Access to `/settings/evolution` page
- API endpoints accessible on port 8765

## Testing Enable Evolution Feature

### 1. Visual Testing via UI

#### Step 1: Access Evolution Settings
1. Navigate to `http://localhost:3000/settings/evolution`
2. Go to the **Overview** tab
3. Locate the "Evolution Intelligence" section

#### Step 2: Test Toggle Functionality
1. **Current State**: Check if "Enable Evolution" toggle is ON (purple) or OFF (gray)
2. **Toggle OFF**: Click the toggle to disable evolution
   - Should show toast notification: "Evolution processing disabled successfully"
   - Toggle should turn gray
   - Auto-Optimization should become disabled (grayed out)
3. **Save Settings**: Click "Save All" button
4. **Verify Persistence**: Refresh the page
   - Toggle should remain in OFF position
   - Setting should be preserved

#### Step 3: Test Re-enabling
1. **Toggle ON**: Click the toggle to enable evolution
   - Should show toast notification: "Evolution processing enabled successfully"
   - Toggle should turn purple
   - Auto-Optimization should become available again
2. **Save and Verify**: Save settings and refresh to confirm persistence

### 2. API Testing

#### Test Current Settings
```bash
curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '.evolution_enabled'
```
**Expected**: `true` or `false` based on current state

#### Test Disabling Evolution
```bash
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"evolution_enabled": false}' | jq '.success'
```
**Expected**: `true`

#### Test Enabling Evolution
```bash
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"evolution_enabled": true}' | jq '.success'
```
**Expected**: `true`

#### Verify Persistence
```bash
curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '.evolution_enabled'
```
**Expected**: Should match the last setting you applied

### 3. Functional Testing

#### Test Evolution Processing
When evolution is **enabled**, you can trigger test operations:

```bash
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/test/trigger-evolution" | jq '.'
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Evolution operation completed successfully",
  "result": {
    "operation_id": "evo_1234567890",
    "operation_type": "ADD|UPDATE|DELETE|NOOP",
    "processing_time_ms": 150,
    "confidence_score": 0.85,
    "memory_affected": "memory_1234",
    "reasoning": "Applied operation based on evolution intelligence analysis",
    "auto_optimization_applied": false,
    "timestamp": 1234567890.123
  }
}
```

When evolution is **disabled**, the same command should return:
```json
{
  "detail": "Evolution processing is disabled"
}
```

## Testing Auto-Optimization Feature

### 1. Visual Testing via UI

#### Step 1: Prerequisites
1. Ensure "Enable Evolution" is turned ON
2. Auto-Optimization toggle should be available (not grayed out)

#### Step 2: Test Auto-Optimization Toggle
1. **Toggle ON**: Click "Auto-Optimization" toggle
   - Should show toast notification: "Auto-optimization enabled successfully"
   - Toggle should turn purple
2. **Save Settings**: Click "Save All" button
3. **Verify Persistence**: Refresh the page
   - Toggle should remain ON
   - Setting should be preserved

#### Step 3: Test Dependency
1. **Disable Evolution**: Turn OFF "Enable Evolution"
   - Auto-Optimization should automatically become disabled and grayed out
2. **Re-enable Evolution**: Turn ON "Enable Evolution"
   - Auto-Optimization should become available again
   - Previous auto-optimization setting should be restored

### 2. API Testing

#### Test Current Auto-Optimization State
```bash
curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '.auto_optimization'
```
**Expected**: `true` or `false`

#### Enable Auto-Optimization
```bash
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"auto_optimization": true}' | jq '.success'
```
**Expected**: `true`

#### Disable Auto-Optimization
```bash
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"auto_optimization": false}' | jq '.success'
```
**Expected**: `true`

#### Check Processing Stats
```bash
curl -s "http://localhost:8765/api/v1/evolution-config/settings/processing-stats" | jq '.'
```

**Expected Response**:
```json
{
  "total_operations_today": 247,
  "successful_operations": 234,
  "failed_operations": 13,
  "success_rate": 94.7,
  "average_processing_time_ms": 156,
  "queue_size": 15,
  "auto_optimization_active": true,
  "last_optimization": "2024-01-15T14:30:00Z",
  "optimizations_performed_today": 8
}
```

### 3. Functional Testing

#### Test Evolution with Auto-Optimization
When both features are enabled, trigger evolution operations:

```bash
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/test/trigger-evolution" | jq '.result.auto_optimization_applied'
```

**Expected**: `true` (when auto-optimization is enabled)

#### Compare Processing Stats
1. **Before enabling auto-optimization**:
   ```bash
   curl -s "http://localhost:8765/api/v1/evolution-config/settings/processing-stats" | jq '.optimizations_performed_today'
   ```
   **Expected**: `0`

2. **After enabling auto-optimization**:
   ```bash
   curl -s "http://localhost:8765/api/v1/evolution-config/settings/processing-stats" | jq '.optimizations_performed_today'
   ```
   **Expected**: `8` (or higher number)

## Advanced Testing Scenarios

### 1. Settings Persistence Test
```bash
# Set both features
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"evolution_enabled": true, "auto_optimization": true}'

# Restart container
docker restart memory-mcp

# Wait for startup (10 seconds)
sleep 10

# Verify settings persisted
curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '{evolution_enabled, auto_optimization}'
```

**Expected**: Both should be `true`

### 2. Reset to Defaults Test
```bash
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/settings/reset" | jq '.success'
```

**Expected**: `true`, and settings should return to defaults

### 3. Configuration File Verification
```bash
# Check if config file was created
docker exec memory-mcp ls -la /app/data/config/

# View current configuration
docker exec memory-mcp cat /app/data/config/evolution_settings.json | jq '.'
```

## Expected Behaviors

### Enable Evolution = ON
- Evolution processing is active
- Memory operations can be triggered
- Processing queue is active
- Auto-optimization becomes available

### Enable Evolution = OFF
- Evolution processing is disabled
- Trigger operations return error
- Processing queue is empty
- Auto-optimization is disabled and grayed out

### Auto-Optimization = ON (requires Evolution = ON)
- Optimization operations are performed automatically
- Processing stats show optimization activity
- Evolution operations include optimization flags
- Performance improvements are applied

### Auto-Optimization = OFF
- No automatic optimizations
- Processing stats show zero optimizations
- Evolution operations work without optimization
- Manual optimization only

## Troubleshooting

### Settings Not Persisting
1. Check container logs: `docker logs memory-mcp`
2. Verify config directory: `docker exec memory-mcp ls -la /app/data/config/`
3. Check file permissions: `docker exec memory-mcp ls -la /app/data/config/evolution_settings.json`

### API Errors
1. Verify container is running: `docker ps --filter name=memory-mcp`
2. Test basic connectivity: `curl http://localhost:8765/api/v1/health/`
3. Check authentication: Ensure you're accessing from the correct domain

### UI Issues
1. Clear browser cache
2. Check browser console for errors
3. Verify API calls are going to correct port (8765)
4. Restart UI container: `docker restart memory-ui`

This testing guide ensures comprehensive validation of both Evolution Intelligence features and their interactions.

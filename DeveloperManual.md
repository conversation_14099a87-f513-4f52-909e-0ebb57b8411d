# Memory Master v2 - Developer Manual

This technical documentation provides comprehensive information for developers contributing to or extending Memory Master v2.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [Folder Structure](#folder-structure)
4. [Development Setup](#development-setup)
5. [Core Components](#core-components)
6. [Evolution Intelligence](#evolution-intelligence)
   - [Architecture](#architecture)
   - [Operation Logic](#operation-logic)
   - [Custom Prompts](#custom-prompts)
   - [DELETE and NOOP Operations Implementation](#delete-and-noop-operations-implementation)
   - [mem0 Response Structure Requirements](#mem0-response-structure-requirements)
   - [Testing DELETE Operations](#testing-delete-operations)
   - [Testing NOOP Operations](#testing-noop-operations)
   - [Debugging Evolution Operations](#debugging-evolution-operations)
   - [mem0 Version Compatibility](#mem0-version-compatibility)
   - [Error Handling and Recovery](#error-handling-and-recovery)
   - [Best Practices](#best-practices)
7. [API Design](#api-design)
8. [Testing Strategy](#testing-strategy)
9. [Deployment](#deployment)
10. [Known Limitations](#known-limitations)
11. [Future Roadmap](#future-roadmap)
12. [Contributing Guidelines](#contributing-guidelines)

## System Architecture

### High-Level Architecture

Memory Master v2 follows a microservices architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │   API Server    │    │  Vector Store   │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Qdrant)      │
│   Port: 3000    │    │   Port: 8765    │    │   Port: 6333    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Database      │
                       │   (Supabase)    │
                       │   Port: 54322   │
                       └─────────────────┘
```

### Component Responsibilities

**API Server (FastAPI)**:
- RESTful API endpoints
- MCP server implementation
- Business logic and services
- Authentication and authorization
- Database operations

**Vector Store (Qdrant)**:
- Vector embeddings storage
- Semantic search capabilities
- Memory similarity calculations
- High-performance retrieval

**Database (Supabase PostgreSQL)**:
- Relational data storage
- User and app management
- Memory metadata
- Evolution intelligence tracking

**Web UI (Next.js)**:
- User interface for memory management
- System monitoring dashboard
- Configuration management
- Analytics visualization

## Database Schema

### Core Tables

**Users Table** (`memory_master.users`):
```sql
CREATE TABLE memory_master.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR UNIQUE NOT NULL,
    name VARCHAR,
    email VARCHAR UNIQUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    supabase_user_id UUID UNIQUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_sign_in_at TIMESTAMP
);
```

**Apps Table** (`memory_master.apps`):
```sql
CREATE TABLE memory_master.apps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES memory_master.users(id),
    name VARCHAR NOT NULL,
    description VARCHAR,
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(owner_id, name)
);
```

**Memories Table** (`memory_master.memories`):
```sql
CREATE TABLE memory_master.memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    content TEXT NOT NULL,
    vector VARCHAR,
    metadata JSONB DEFAULT '{}',
    state VARCHAR DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    archived_at TIMESTAMP,
    deleted_at TIMESTAMP
);
```

### Evolution Intelligence Tables

**Evolution Operations** (`memory_master.evolution_operations`):
```sql
CREATE TABLE memory_master.evolution_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    memory_id UUID REFERENCES memory_master.memories(id),
    operation_type VARCHAR NOT NULL, -- ADD/UPDATE/DELETE/NOOP
    candidate_fact TEXT NOT NULL,
    existing_memory_content TEXT,
    similarity_score FLOAT,
    confidence_score FLOAT,
    reasoning TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);
```

**Evolution Insights** (`memory_master.evolution_insights`):
```sql
CREATE TABLE memory_master.evolution_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    date DATE NOT NULL,
    total_operations INTEGER DEFAULT 0,
    add_operations INTEGER DEFAULT 0,
    update_operations INTEGER DEFAULT 0,
    delete_operations INTEGER DEFAULT 0,
    noop_operations INTEGER DEFAULT 0,
    learning_efficiency FLOAT,
    conflict_resolution_count INTEGER DEFAULT 0,
    average_confidence FLOAT,
    average_similarity FLOAT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, app_id, date)
);
```

### Relationships and Constraints

- **Users → Apps**: One-to-many (user can own multiple apps)
- **Users → Memories**: One-to-many (user can have multiple memories)
- **Apps → Memories**: One-to-many (app can create multiple memories)
- **Memories → Evolution Operations**: One-to-many (memory can have multiple operations)
- **Cascade Deletes**: User deletion cascades to apps, memories, and evolution data

## Folder Structure

```
memory-master-v2/
├── api/                           # Backend API & MCP Server
│   ├── app/                      # FastAPI application
│   │   ├── __init__.py
│   │   ├── config.py             # Configuration management
│   │   ├── database.py           # Database connection
│   │   ├── models.py             # SQLAlchemy models
│   │   ├── schemas.py            # Pydantic schemas
│   │   ├── mcp_server.py         # MCP server implementation
│   │   ├── memory_service.py     # Memory business logic
│   │   ├── memory_utils.py       # Memory utilities
│   │   ├── health_service.py     # Health monitoring
│   │   ├── auth/                 # Authentication
│   │   │   ├── middleware.py     # Auth middleware
│   │   │   └── supabase.py       # Supabase auth
│   │   ├── routers/              # API route handlers
│   │   │   ├── memories.py       # Memory endpoints
│   │   │   ├── apps.py           # App management
│   │   │   ├── stats.py          # Analytics
│   │   │   ├── config.py         # Configuration
│   │   │   └── auth.py           # Authentication
│   │   ├── services/             # Business logic services
│   │   │   └── evolution_service.py # Evolution intelligence
│   │   ├── utils/                # Utility functions
│   │   │   ├── memory.py         # Memory client utilities
│   │   │   ├── evolution_prompts.py # Custom prompts
│   │   │   ├── config_manager.py # Configuration management
│   │   │   └── permissions.py    # Access control
│   │   └── database/             # Database services
│   │       ├── base.py           # Base model
│   │       └── service.py        # Database operations
│   ├── alembic/                  # Database migrations
│   │   ├── versions/             # Migration files
│   │   ├── env.py                # Alembic environment
│   │   └── alembic.ini           # Alembic configuration
│   ├── tests/                    # Test suite
│   │   ├── conftest.py           # Test configuration
│   │   ├── test_evolution_*.py   # Evolution tests
│   │   └── test_*.py             # Other tests
│   ├── Dockerfile                # API container
│   ├── main.py                   # FastAPI entry point
│   └── requirements.txt          # Python dependencies
├── ui/                           # Frontend Web Dashboard
│   ├── app/                      # Next.js app directory
│   │   ├── layout.tsx            # Root layout
│   │   ├── page.tsx              # Home page
│   │   ├── dashboard/            # Dashboard pages
│   │   ├── memories/             # Memory management
│   │   ├── apps/                 # App management
│   │   └── settings/             # Configuration
│   ├── components/               # React components
│   │   ├── ui/                   # Base UI components
│   │   ├── memory/               # Memory components
│   │   ├── app/                  # App components
│   │   └── charts/               # Analytics charts
│   ├── hooks/                    # Custom React hooks
│   ├── lib/                      # Utility libraries
│   ├── store/                    # State management
│   ├── styles/                   # CSS and styling
│   ├── Dockerfile                # UI container
│   ├── package.json              # Node.js dependencies
│   └── next.config.mjs           # Next.js configuration
├── backup-scripts/               # Automated backup system
├── docker-compose.yml            # Multi-container orchestration
├── README.md                     # Project overview
├── UserManual.md                 # User documentation
└── DeveloperManual.md            # This file
```

## Development Setup

### Local Development Environment

1. **Prerequisites**:
   ```bash
   # Required
   docker --version          # 20.10+
   docker-compose --version  # 1.29+
   python --version          # 3.12+
   node --version            # 18+
   
   # Optional for local development
   pnpm --version            # 8+
   ```

2. **Environment Configuration**:
   ```bash
   # Copy environment templates
   cp api/.env.example api/.env
   cp ui/.env.example ui/.env
   
   # Edit with your values
   nano api/.env
   nano ui/.env
   ```

3. **Database Setup**:
   ```bash
   # Start Qdrant vector store
   docker-compose up -d mem0_store
   
   # Run database migrations
   cd api
   alembic upgrade head
   ```

4. **Local API Development**:
   ```bash
   cd api
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate on Windows
   pip install -r requirements.txt
   uvicorn main:app --reload --host 0.0.0.0 --port 8765
   ```

5. **Local UI Development**:
   ```bash
   cd ui
   pnpm install
   pnpm dev
   ```

### Development Tools

**Code Quality**:
- **Linting**: ESLint for TypeScript, flake8 for Python
- **Formatting**: Prettier for TypeScript, black for Python
- **Type Checking**: TypeScript strict mode, mypy for Python

**Testing**:
- **Backend**: pytest with asyncio support
- **Frontend**: Jest with React Testing Library
- **Integration**: Docker-based test environment

**Database**:
- **Migrations**: Alembic for schema changes
- **Seeding**: Custom scripts for test data
- **Backup**: Automated scripts in backup-scripts/

## Core Components

### Memory Service Layer

The `MemoryService` class handles all memory operations:

```python
class MemoryService:
    def add_memory(self, text: str, user_id: str, client_name: str) -> Tuple[bool, str, Optional[str]]
    def search_memories(self, query: str, user_id: str, client_name: str, limit: int = 10) -> Tuple[bool, str, List[Dict]]
    def list_memories(self, user_id: str, client_name: str, limit: int = 50, offset: int = 0) -> Tuple[bool, str, List[Dict]]
```

**Key Features**:
- Automatic text chunking for large inputs
- Vector embedding generation
- Memory state management
- Access control validation

### MCP Server Implementation

The MCP server provides standardized tools for AI applications:

```python
# Available MCP Tools
@mcp.tool()
async def add_memories(text: str) -> str:
    """Store new memories with automatic processing."""

@mcp.tool()
async def search_memory(query: str, limit: int = 10) -> str:
    """Semantic search across stored memories."""

@mcp.tool()
async def list_memories(limit: int = 50, offset: int = 0) -> str:
    """Browse memories with filtering options."""
```

### Configuration Management

Dynamic configuration system with hot-reload capabilities:

```python
class ConfigManager:
    def get_config(self, key: str) -> Any
    def update_config(self, key: str, value: Any) -> bool
    def register_listener(self, callback: Callable) -> None
```

## Evolution Intelligence

### Architecture

The Evolution Intelligence system automatically optimizes memory storage through intelligent operations:

**Components**:
1. **EvolutionService**: Orchestrates evolution operations
2. **Custom Prompts**: Technical domain-optimized prompts
3. **Operation Types**: ADD/UPDATE/DELETE/NOOP decisions
4. **Analytics**: Learning efficiency tracking

### Operation Logic

```python
class EvolutionOperationType(enum.Enum):
    ADD = "ADD"        # Store new information
    UPDATE = "UPDATE"  # Enhance existing memory
    DELETE = "DELETE"  # Remove outdated information
    NOOP = "NOOP"      # No operation needed
```

**Decision Criteria**:
- **Similarity Score**: <0.85 → ADD, >0.85 → consider UPDATE/DELETE/NOOP
- **Conflict Detection**: Contradictory information → DELETE + ADD
- **Redundancy Check**: Duplicate information → NOOP
- **Enhancement**: Complementary information → UPDATE

### Custom Prompts

Technical domain-optimized prompts for better evolution decisions:

```python
TECHNICAL_FACT_EXTRACTION_PROMPT = """
You are an expert technical fact extractor for a developer's memory system.
Extract relevant technical facts that would be valuable for future reference.
Focus on: technologies, frameworks, decisions, preferences, solutions, patterns.
"""

TECHNICAL_UPDATE_MEMORY_PROMPT = """
You are an expert technical memory evolution system.
Decide how to handle new information against existing memories.
Prioritize recent, specific information over old, general information.
"""
```

### DELETE and NOOP Operations Implementation

#### Problem Statement

The evolution system was initially only processing ADD and UPDATE operations correctly, while DELETE and NOOP operations remained at low counts despite appropriate test scenarios. This was due to:

1. **Custom Prompt Response Format Mismatch**: mem0 expected specific response structure
2. **Response Parsing Logic Gap**: System wasn't handling mem0's custom prompt responses
3. **Event Field Mapping Issues**: Incorrect mapping between mem0 events and system operations

#### mem0 Response Structure Requirements

**Standard mem0 Response (Basic Operations)**:
```json
{
  "results": [
    {
      "id": "memory_uuid",
      "text": "memory content",
      "metadata": {...}
    }
  ]
}
```

**Custom Prompt Response (Evolution Operations)**:
```json
{
  "memory": [
    {
      "id": "memory_uuid",
      "text": "memory content",
      "event": "ADD|UPDATE|DELETE|NONE",
      "old_memory": "previous content (for UPDATE/DELETE)"
    }
  ]
}
```

#### Custom Prompt Format Fix

**Before (Incorrect Format)**:
```python
# Custom prompt returned array directly
OUTPUT_FORMAT = """
Return a JSON array of operations:
[{
  "id": "0",
  "text": "updated memory text",
  "event": "UPDATE",
  "old_memory": "previous text"
}]
"""
```

**After (Correct Format)**:
```python
# Custom prompt returns object with memory array
OUTPUT_FORMAT = """
Return a JSON object with a "memory" array:
{
  "memory": [{
    "id": "0",
    "text": "updated memory text",
    "event": "UPDATE",
    "old_memory": "previous text"
  }]
}
"""
```

#### Response Parsing Enhancement

**Enhanced Parsing Logic**:
```python
# Priority order for response parsing
def _track_evolution_operations(self, mem0_response, user_id, app_id, original_text):
    results = []

    # Format 1: Custom prompt format (PRIORITY)
    if isinstance(mem0_response, dict) and 'memory' in mem0_response:
        self.logger.info(f"Found 'memory' array with {len(mem0_response['memory'])} items")
        for memory in mem0_response['memory']:
            operation_data = self._parse_mem0_memory(memory, user_id, app_id, original_text)
            if operation_data:
                operations.append(operation_data)

    # Format 2: Standard format (FALLBACK)
    elif isinstance(mem0_response, dict) and 'results' in mem0_response:
        self.logger.info(f"Found results array with {len(mem0_response['results'])} items")
        for result in mem0_response['results']:
            operation_data = self._parse_mem0_result(result, user_id, app_id, original_text)
            if operation_data:
                operations.append(operation_data)
```

#### Event Mapping Implementation

**Event Type Mapping**:
```python
def _parse_mem0_memory(self, memory, user_id, app_id, original_text):
    # Extract event from memory object
    if isinstance(memory, dict):
        event = memory.get('event', 'ADD')
        text = memory.get('text', original_text)
        memory_id = memory.get('id')
        old_memory = memory.get('old_memory', '')
    else:
        event = getattr(memory, 'event', 'ADD')
        text = getattr(memory, 'text', original_text)
        memory_id = getattr(memory, 'id', None)
        old_memory = getattr(memory, 'old_memory', '')

    # Map mem0 events to system operations
    operation_type_map = {
        'ADD': 'ADD',
        'UPDATE': 'UPDATE',
        'DELETE': 'DELETE',
        'NONE': 'NOOP',  # mem0 uses 'NONE' for no-operation
        'NOOP': 'NOOP'   # fallback
    }

    operation_type = operation_type_map.get(event.upper() if event else 'ADD', 'ADD')

    # Generate contextual reasoning
    reasoning_map = {
        'ADD': f'New memory added: {text[:100]}...',
        'UPDATE': f'Memory updated from "{old_memory[:50]}..." to "{text[:50]}..."',
        'DELETE': f'Memory deleted: {old_memory[:100]}...',
        'NOOP': f'No change needed - memory already exists: {text[:100]}...'
    }

    return {
        'user_id': user_id,
        'app_id': app_id,
        'operation_type': operation_type,
        'candidate_fact': str(text)[:500],
        'confidence_score': 0.9,
        'reasoning': reasoning_map.get(operation_type, 'Memory processed successfully'),
        'metadata': {
            'event': event,
            'old_memory': old_memory[:200] if old_memory else '',
            'memory_id': str(memory_id) if memory_id else None,
            'custom_prompt_used': True
        }
    }
```

#### Testing DELETE Operations

**Test Scenario 1: Explicit Contradiction**
```python
# Step 1: Add initial memory
add_memory("I prefer Python for data science projects")

# Step 2: Add contradictory information (should trigger DELETE + ADD)
add_memory("I no longer use Python, I have switched to R for all data science work")

# Expected Result:
# - DELETE: "I prefer Python for data science projects"
# - ADD: "I prefer R for data science projects"
```

**Test Scenario 2: Technology Migration**
```python
# Step 1: Add technology preference
add_memory("I use MySQL database for web applications")

# Step 2: Add migration information (should trigger UPDATE or DELETE + ADD)
add_memory("I have migrated from MySQL to PostgreSQL for better JSON support")

# Expected Result:
# - DELETE: "I use MySQL database for web applications"
# - ADD: "I use PostgreSQL database for better JSON support"
```

#### Testing NOOP Operations

**Test Scenario 1: Exact Duplication**
```python
# Step 1: Add memory
add_memory("I use TypeScript for frontend development")

# Step 2: Add identical information (should trigger NOOP)
add_memory("I use TypeScript for frontend development")

# Expected Result:
# - NOOP: No change needed - memory already exists
```

**Test Scenario 2: Semantic Duplication**
```python
# Step 1: Add memory
add_memory("I prefer React for building user interfaces")

# Step 2: Add semantically similar information (should trigger NOOP)
add_memory("React is my preferred choice for UI development")

# Expected Result:
# - NOOP: No change needed - semantically equivalent information exists
```

#### Debugging Evolution Operations

**Enable Debug Logging**:
```python
# Add to evolution_service.py for detailed debugging
self.logger.info(f"🔍 EVOLUTION DEBUG: Processing mem0 response")
self.logger.info(f"🔍 EVOLUTION DEBUG: Response type: {type(mem0_response)}")
self.logger.info(f"🔍 EVOLUTION DEBUG: Full response: {str(mem0_response)}")

# Check for memory array
if isinstance(mem0_response, dict) and 'memory' in mem0_response:
    self.logger.info(f"🔍 EVOLUTION DEBUG: Found 'memory' array with {len(mem0_response['memory'])} items")
    for i, memory in enumerate(mem0_response['memory']):
        self.logger.info(f"🔍 EVOLUTION DEBUG: Memory {i}: {memory}")
```

**Monitor Container Logs**:
```bash
# Watch evolution operations in real-time
docker logs memory-mcp -f | grep "EVOLUTION DEBUG"

# Check for custom prompt usage
docker logs memory-mcp | grep "Using custom.*prompt"

# Monitor operation tracking
docker logs memory-mcp | grep "evolution_service"
```

**Verify Custom Prompts**:
```bash
# Check if custom prompts are loaded
docker logs memory-mcp | grep "✅ Using custom.*prompt"

# Expected output:
# ✅ Using custom fact extraction prompt from database configuration (2238 chars)
# ✅ Using custom update memory prompt from database configuration (3723 chars)
```

#### Common Issues and Solutions

**Issue 1: Only ADD Operations Detected**
```
Symptoms: DELETE and NOOP counts remain at 0
Cause: Custom prompts not returning correct format
Solution: Verify custom prompt returns {"memory": [...]} structure
```

**Issue 2: mem0 Response Parsing Errors**
```
Symptoms: "No recognizable evolution data structure found" warnings
Cause: Response format doesn't match expected structure
Solution: Check mem0 version compatibility and response format
```

**Issue 3: Event Mapping Failures**
```
Symptoms: All operations mapped to ADD despite correct events
Cause: Event field missing or incorrect in mem0 response
Solution: Verify custom prompt includes "event" field in output
```

#### Performance Considerations

**Memory Usage**:
- Custom prompts increase LLM token usage by ~30%
- Evolution tracking adds ~50ms per memory operation
- Database storage grows with operation history

**Optimization Strategies**:
```python
# Batch evolution operations
def batch_track_evolution(self, operations_batch):
    with SessionLocal() as db:
        for operation_data in operations_batch:
            self.store_evolution_operation(operation_data)
        db.commit()

# Implement operation deduplication
def deduplicate_operations(self, operations):
    seen = set()
    unique_ops = []
    for op in operations:
        key = (op['operation_type'], op['candidate_fact'][:100])
        if key not in seen:
            seen.add(key)
            unique_ops.append(op)
    return unique_ops
```

#### Integration with Analytics

**Real-time Metrics**:
```python
# Track operation distribution
operation_counts = {
    'ADD': len([op for op in operations if op['operation_type'] == 'ADD']),
    'UPDATE': len([op for op in operations if op['operation_type'] == 'UPDATE']),
    'DELETE': len([op for op in operations if op['operation_type'] == 'DELETE']),
    'NOOP': len([op for op in operations if op['operation_type'] == 'NOOP'])
}

# Calculate learning efficiency
total_operations = sum(operation_counts.values())
learning_efficiency = (operation_counts['UPDATE'] + operation_counts['DELETE']) / total_operations * 100
```

**Dashboard Integration**:
```typescript
// Frontend component for evolution monitoring
const EvolutionMonitor = () => {
  const { data: metrics } = useEvolutionMetrics();

  return (
    <div className="evolution-metrics">
      <MetricCard title="DELETE Operations" value={metrics.delete_count} />
      <MetricCard title="NOOP Operations" value={metrics.noop_count} />
      <MetricCard title="Learning Efficiency" value={`${metrics.learning_efficiency}%`} />
    </div>
  );
};
```

#### mem0 Version Compatibility

**Supported Versions**:
- **mem0ai >= 0.1.112**: Full custom prompt support with evolution operations
- **mem0ai 0.1.107-0.1.111**: Limited custom prompt support, may not process DELETE/NOOP correctly
- **mem0ai < 0.1.107**: No custom prompt support, only basic ADD operations

**Version Check**:
```bash
# Check current mem0 version in container
docker exec memory-mcp pip show mem0ai

# Expected output for working version:
# Name: mem0ai
# Version: 0.1.112
# Summary: Memory layer for AI applications
```

**Upgrade Process**:
```bash
# Update requirements.txt
echo "mem0ai>=0.1.112" >> api/requirements.txt

# Rebuild container
docker-compose build memory-mcp
docker-compose up -d memory-mcp
```

#### Custom Prompt Configuration

**Database Storage**:
```sql
-- Custom prompts are stored in configuration table
SELECT key, value FROM memory_master.configuration
WHERE key IN ('custom_fact_extraction_prompt', 'custom_update_memory_prompt');
```

**Runtime Loading**:
```python
# Custom prompts loaded at memory client initialization
def load_custom_prompts(self):
    config_manager = ConfigManager()

    # Load fact extraction prompt
    fact_prompt = config_manager.get_config('custom_fact_extraction_prompt')
    if fact_prompt:
        self.logger.info(f"✅ Using custom fact extraction prompt ({len(fact_prompt)} chars)")

    # Load update memory prompt
    update_prompt = config_manager.get_config('custom_update_memory_prompt')
    if update_prompt:
        self.logger.info(f"✅ Using custom update memory prompt ({len(update_prompt)} chars)")

    return {
        'custom_fact_extraction_prompt': fact_prompt,
        'custom_update_memory_prompt': update_prompt
    }
```

**Prompt Validation**:
```python
def validate_custom_prompt(prompt_text: str) -> bool:
    """Validate custom prompt format and requirements."""
    required_elements = [
        'OUTPUT FORMAT',
        'memory',
        'event',
        'ADD', 'UPDATE', 'DELETE', 'NONE'
    ]

    for element in required_elements:
        if element not in prompt_text:
            logger.warning(f"Custom prompt missing required element: {element}")
            return False

    # Check JSON structure example
    if '{"memory": [' not in prompt_text:
        logger.warning("Custom prompt missing correct JSON structure example")
        return False

    return True
```

#### Error Handling and Recovery

**Graceful Degradation**:
```python
def handle_evolution_failure(self, error, mem0_response, original_text):
    """Handle evolution processing failures gracefully."""
    self.logger.error(f"Evolution processing failed: {error}")

    # Fall back to basic ADD operation
    fallback_operation = {
        'user_id': self.user_id,
        'app_id': self.app_id,
        'operation_type': 'ADD',
        'candidate_fact': original_text[:500],
        'confidence_score': 0.5,  # Lower confidence for fallback
        'reasoning': f'Fallback operation due to evolution failure: {str(error)[:200]}',
        'metadata': {
            'fallback_operation': True,
            'original_error': str(error),
            'response_type': str(type(mem0_response))
        }
    }

    return [fallback_operation]
```

**Recovery Mechanisms**:
```python
def attempt_evolution_recovery(self, mem0_response):
    """Attempt to recover evolution data from malformed responses."""

    # Try to extract from string representation
    if isinstance(mem0_response, str):
        try:
            import json
            parsed = json.loads(mem0_response)
            return self._track_evolution_operations(parsed, self.user_id, self.app_id, self.original_text)
        except json.JSONDecodeError:
            pass

    # Try to extract from object attributes
    if hasattr(mem0_response, '__dict__'):
        attrs = vars(mem0_response)
        if 'memory' in attrs or 'results' in attrs:
            return self._track_evolution_operations(attrs, self.user_id, self.app_id, self.original_text)

    # Log recovery failure
    self.logger.warning("Evolution recovery failed, using fallback operation")
    return self.handle_evolution_failure("Recovery failed", mem0_response, self.original_text)
```

#### Best Practices

**Custom Prompt Design**:
1. **Be Explicit**: Clearly specify expected output format
2. **Include Examples**: Provide concrete examples for each operation type
3. **Handle Edge Cases**: Account for ambiguous or incomplete information
4. **Validate Output**: Include format validation in prompts

**Performance Optimization**:
1. **Batch Operations**: Process multiple memories together when possible
2. **Cache Responses**: Cache similar evolution decisions to reduce LLM calls
3. **Async Processing**: Use background tasks for non-critical evolution tracking
4. **Monitor Costs**: Track LLM token usage and optimize prompts accordingly

**Monitoring and Alerting**:
```python
# Set up alerts for evolution system health
def check_evolution_health():
    recent_ops = get_recent_operations(hours=24)

    # Alert if no DELETE/NOOP operations in 24 hours
    delete_count = len([op for op in recent_ops if op.operation_type == 'DELETE'])
    noop_count = len([op for op in recent_ops if op.operation_type == 'NOOP'])

    if delete_count == 0 and noop_count == 0 and len(recent_ops) > 10:
        send_alert("Evolution system may not be processing DELETE/NOOP operations correctly")

    # Alert if learning efficiency drops below threshold
    learning_efficiency = calculate_learning_efficiency(recent_ops)
    if learning_efficiency < 20:  # Less than 20% UPDATE/DELETE operations
        send_alert(f"Learning efficiency low: {learning_efficiency}%")
```

#### Summary

The DELETE and NOOP operations implementation represents a critical enhancement to Memory Master v2's evolution intelligence system. Key achievements:

**Technical Improvements**:
- ✅ **Fixed Custom Prompt Format**: Aligned with mem0's expected `{"memory": [...]}` structure
- ✅ **Enhanced Response Parsing**: Added priority handling for custom prompt responses
- ✅ **Improved Event Mapping**: Correctly maps mem0's `NONE` events to `NOOP` operations
- ✅ **Comprehensive Debugging**: Added detailed logging for troubleshooting evolution issues

**Operational Benefits**:
- 🎯 **Accurate DELETE Detection**: System now properly identifies and processes memory deletions
- 🔄 **Effective NOOP Recognition**: Redundant information is correctly identified and skipped
- 📊 **Better Analytics**: More accurate learning efficiency metrics and operation distribution
- 🛡️ **Robust Error Handling**: Graceful degradation when evolution processing fails

**Developer Experience**:
- 📖 **Comprehensive Documentation**: Detailed examples and troubleshooting guides
- 🔧 **Debug Tools**: Built-in logging and monitoring capabilities
- 🧪 **Test Scenarios**: Specific test cases for validating DELETE and NOOP functionality
- ⚡ **Performance Optimization**: Best practices for efficient evolution processing

**Validation Checklist**:
```bash
# 1. Verify mem0 version compatibility
docker exec memory-mcp pip show mem0ai | grep "Version: 0.1.112"

# 2. Check custom prompt loading
docker logs memory-mcp | grep "✅ Using custom.*prompt"

# 3. Monitor evolution operations
docker logs memory-mcp | grep "🔍 EVOLUTION DEBUG"

# 4. Test DELETE operations
# Add contradictory information and verify DELETE count increases

# 5. Test NOOP operations
# Add duplicate information and verify NOOP count increases

# 6. Verify analytics accuracy
# Check evolution dashboard for proper operation distribution
```

This implementation ensures that Memory Master v2's evolution intelligence operates at full capacity, providing users with a truly intelligent memory system that can add, update, delete, and recognize redundant information with high accuracy.

## API Design

### RESTful Endpoints

**Memory Management**:
```
POST   /api/v1/memories              # Create memory
GET    /api/v1/memories              # List memories
GET    /api/v1/memories/search       # Search memories
GET    /api/v1/memories/{id}         # Get memory
PUT    /api/v1/memories/{id}         # Update memory
DELETE /api/v1/memories/{id}         # Delete memory
```

**App Management**:
```
POST   /api/v1/apps                  # Create app
GET    /api/v1/apps                  # List apps
GET    /api/v1/apps/{id}             # Get app details
PUT    /api/v1/apps/{id}             # Update app
DELETE /api/v1/apps/{id}             # Delete app
```

**Evolution Intelligence**:
```
GET    /api/v1/evolution/metrics     # Get evolution metrics
GET    /api/v1/evolution/monitor     # Monitor evolution activity
GET    /api/v1/evolution/insights    # Get learning insights
```

### Response Formats

**Standard Response**:
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Error Response**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {...}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Testing Strategy

### Test Categories

**Unit Tests**:
- Individual function testing
- Mock external dependencies
- Fast execution (<1s per test)

**Integration Tests**:
- Database operations
- API endpoint testing
- Service layer interactions

**End-to-End Tests**:
- Full workflow testing
- Docker-based environment
- Real database interactions

### Test Structure

```python
# Example test structure
class TestEvolutionService:
    def setup_method(self):
        """Setup test database and fixtures."""
        
    def test_add_operation_creates_record(self):
        """Test that ADD operations are properly recorded."""
        
    def test_update_operation_modifies_existing(self):
        """Test that UPDATE operations modify existing memories."""
        
    def teardown_method(self):
        """Cleanup test data."""
```

### Running Tests

```bash
# Backend tests
cd api
pytest tests/ -v --cov=app

# Frontend tests
cd ui
pnpm test

# Integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## Deployment

### Production Deployment

**Docker Compose Production**:
```yaml
services:
  openmemory-mcp:
    image: mem0/openmemory-mcp:latest
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
```

**Environment Variables**:
```bash
# Required
DATABASE_URL=postgresql://user:pass@host:port/db
OPENAI_API_KEY=sk-...
USER=unique-user-id

# Optional
API_KEY=optional-api-key
MIGRATION_MODE=supabase_only
```

### Monitoring

**Health Checks**:
- `/health` endpoint for API server
- Qdrant dashboard at `:6333/dashboard`
- Database connection monitoring

**Metrics**:
- Memory operation latency
- Evolution intelligence efficiency
- Database query performance
- Vector store utilization

## Known Limitations

### Current Constraints

1. **Text Processing**:
   - Large texts (>2000 words) are automatically chunked
   - mem0 may filter out content deemed "not memorable"
   - Claude Desktop has context inconsistency issues

2. **Performance**:
   - Vector embeddings generation adds latency
   - Large memory collections may slow search
   - Concurrent operations may cause conflicts

3. **Evolution Intelligence**:
   - Requires mem0 v1.1+ for custom prompts
   - Decision accuracy depends on LLM quality
   - May generate false positives for similar content

4. **Scalability**:
   - Single Qdrant instance limits throughput
   - Database connections may become bottleneck
   - Memory usage grows with vector storage

### Workarounds

**Text Length Issues**:
- Use concise, specific memories
- Break large documents into logical chunks
- Add relevant metadata for better retrieval

**Performance Optimization**:
- Enable connection pooling
- Use database indexes effectively
- Implement caching for frequent queries

## Future Roadmap

### Short-term (Next 3 months)

1. **Performance Improvements**:
   - Implement Redis caching layer
   - Optimize database queries
   - Add connection pooling

2. **Enhanced Evolution Intelligence**:
   - Improve decision accuracy
   - Add confidence thresholds
   - Implement conflict resolution strategies

3. **Better MCP Integration**:
   - Support more MCP-compatible tools
   - Improve error handling
   - Add batch operations

### Medium-term (3-6 months)

1. **Multi-tenant Support**:
   - Organization-level memory sharing
   - Role-based access control
   - Team collaboration features

2. **Advanced Analytics**:
   - Memory usage patterns
   - Learning efficiency trends
   - Predictive insights

3. **API Enhancements**:
   - GraphQL support
   - Webhook notifications
   - Bulk operations

### Long-term (6+ months)

1. **Distributed Architecture**:
   - Multiple Qdrant instances
   - Horizontal scaling
   - Load balancing

2. **Advanced AI Features**:
   - Custom embedding models
   - Domain-specific optimizations
   - Automated memory curation

3. **Enterprise Features**:
   - SSO integration
   - Audit logging
   - Compliance tools

## Contributing Guidelines

### Code Standards

**Python (Backend)**:
- Follow PEP 8 style guide
- Use type hints for all functions
- Document with docstrings
- Maximum line length: 100 characters

**TypeScript (Frontend)**:
- Use strict TypeScript configuration
- Follow React best practices
- Use functional components with hooks
- Implement proper error boundaries

### Commit Guidelines

**Commit Message Format**:
```
type(scope): description

[optional body]

[optional footer]
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions/modifications
- `chore`: Maintenance tasks

### Pull Request Process

1. **Create Feature Branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement Changes**:
   - Write tests for new functionality
   - Update documentation
   - Follow code standards

3. **Submit Pull Request**:
   - Provide clear description
   - Link related issues
   - Include test results

4. **Code Review**:
   - Address reviewer feedback
   - Ensure CI passes
   - Update based on suggestions

### Development Rules

**Database Changes**:
- Always create Alembic migrations
- Test migrations on sample data
- Document schema changes

**API Changes**:
- Maintain backward compatibility
- Update API documentation
- Version breaking changes

**Testing Requirements**:
- Minimum 80% code coverage
- All tests must pass
- Include integration tests for new features

---

This developer manual provides the foundation for contributing to Memory Master v2. For specific implementation details, refer to the inline code documentation and test examples.
